from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from .models import TradingOrder, UserPosition, TradingRecord
from .serializers import (
    TradingOrderSerializer, CreateOrderSerializer, UserPositionSerializer,
    TradingRecordSerializer, ProfitSummarySerializer
)
from .services import TradingService


@method_decorator(csrf_exempt, name='dispatch')
class CreateOrderView(APIView):
    """创建交易委托"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = CreateOrderSerializer(data=request.data)
        if serializer.is_valid():
            service = TradingService()
            result = service.create_order(
                user=request.user,
                stock_code=serializer.validated_data['stock_code'],
                order_type=serializer.validated_data['order_type'],
                order_price=serializer.validated_data['order_price'],
                order_quantity=serializer.validated_data['order_quantity']
            )
            
            if result['success']:
                order_serializer = TradingOrderSerializer(result['order'])
                return Response({
                    'message': '委托创建成功',
                    'order': order_serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({'error': result['message']}, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CurrentOrdersView(ListAPIView):
    """当前委托列表"""
    serializer_class = TradingOrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TradingOrder.objects.filter(
            user=self.request.user,
            order_status__in=[1, 2]  # 待成交、部分成交
        ).order_by('-order_time')


class HistoryOrdersView(ListAPIView):
    """历史委托列表"""
    serializer_class = TradingOrderSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TradingOrder.objects.filter(
            user=self.request.user,
            order_status__in=[3, 4]  # 全部成交、已撤销
        ).order_by('-order_time')


@method_decorator(csrf_exempt, name='dispatch')
class CancelOrderView(APIView):
    """撤销委托"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, order_id):
        service = TradingService()
        result = service.cancel_order(order_id, request.user)
        
        if result['success']:
            return Response({'message': result['message']}, status=status.HTTP_200_OK)
        else:
            return Response({'error': result['message']}, status=status.HTTP_400_BAD_REQUEST)


class CurrentPositionsView(ListAPIView):
    """当前持仓列表"""
    serializer_class = UserPositionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        # 更新持仓价格
        service = TradingService()
        service.update_all_positions_price(self.request.user)
        
        return UserPosition.objects.filter(
            user=self.request.user,
            total_quantity__gt=0
        ).order_by('-last_update_time')


class HistoryPositionsView(ListAPIView):
    """历史持仓列表"""
    serializer_class = UserPositionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserPosition.objects.filter(
            user=self.request.user,
            total_quantity=0
        ).order_by('-last_update_time')


class TradingRecordsView(ListAPIView):
    """交易记录列表"""
    serializer_class = TradingRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TradingRecord.objects.filter(
            user=self.request.user
        ).order_by('-trade_time')


class ProfitSummaryView(APIView):
    """收益汇总"""
    permission_classes = [permissions.IsAuthenticated]
    
    def get(self, request):
        service = TradingService()
        summary = service.get_user_profit_summary(request.user)
        
        if summary:
            serializer = ProfitSummarySerializer(summary)
            return Response(serializer.data)
        else:
            return Response({'error': '获取收益汇总失败'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@csrf_exempt
def quick_buy(request, stock_code):
    """快速买入"""
    quantity = request.data.get('quantity')
    if not quantity or quantity <= 0:
        return Response({'error': '请提供有效的买入数量'}, status=status.HTTP_400_BAD_REQUEST)
    
    # 获取当前价格作为委托价格
    from stocks.services import StockDataService
    stock_service = StockDataService()
    current_price = stock_service.get_current_price(stock_code)
    
    if not current_price:
        return Response({'error': '无法获取当前价格'}, status=status.HTTP_400_BAD_REQUEST)
    
    # 创建买入委托
    service = TradingService()
    result = service.create_order(
        user=request.user,
        stock_code=stock_code,
        order_type=1,  # 买入
        order_price=current_price,
        order_quantity=quantity
    )
    
    if result['success']:
        order_serializer = TradingOrderSerializer(result['order'])
        return Response({
            'message': '买入委托创建成功',
            'order': order_serializer.data
        }, status=status.HTTP_201_CREATED)
    else:
        return Response({'error': result['message']}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
@csrf_exempt
def quick_sell(request, stock_code):
    """快速卖出"""
    quantity = request.data.get('quantity')
    if not quantity or quantity <= 0:
        return Response({'error': '请提供有效的卖出数量'}, status=status.HTTP_400_BAD_REQUEST)
    
    # 获取当前价格作为委托价格
    from stocks.services import StockDataService
    stock_service = StockDataService()
    current_price = stock_service.get_current_price(stock_code)
    
    if not current_price:
        return Response({'error': '无法获取当前价格'}, status=status.HTTP_400_BAD_REQUEST)
    
    # 创建卖出委托
    service = TradingService()
    result = service.create_order(
        user=request.user,
        stock_code=stock_code,
        order_type=2,  # 卖出
        order_price=current_price,
        order_quantity=quantity
    )
    
    if result['success']:
        order_serializer = TradingOrderSerializer(result['order'])
        return Response({
            'message': '卖出委托创建成功',
            'order': order_serializer.data
        }, status=status.HTTP_201_CREATED)
    else:
        return Response({'error': result['message']}, status=status.HTTP_400_BAD_REQUEST)
