#!/usr/bin/env python
"""
股票数据爬取脚本
使用AKShare和Tushare获取股票数据并存储到MySQL数据库
"""

import os
import sys
import django
from datetime import datetime, timedelta
import pandas as pd
import akshare as ak
import tushare as ts
import time
import logging

# 添加Django项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock_management.settings')
django.setup()

from stocks.models import Stock, StockDailyData, StockMinuteData, HS300Data

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Tushare配置
TUSHARE_TOKEN = '4663a4811b61b7ae71fbee8e0e84e2600c8d03530ce45ace9f58c3cd'
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

class StockDataFetcher:
    def __init__(self):
        self.sleep_time = 0.2  # 请求间隔
    
    def fetch_stock_list(self):
        """获取股票列表"""
        logger.info("开始获取股票列表...")
        
        try:
            # 使用AKShare获取股票列表
            stock_list = ak.stock_info_a_code_name()
            
            for _, row in stock_list.iterrows():
                stock_code = row['code']
                stock_name = row['name']
                
                # 判断市场
                if stock_code.startswith('6'):
                    market = 'SH'
                elif stock_code.startswith(('0', '3')):
                    market = 'SZ'
                else:
                    continue
                
                # 创建或更新股票信息
                stock, created = Stock.objects.get_or_create(
                    stock_code=stock_code,
                    defaults={
                        'stock_name': stock_name,
                        'market': market,
                        'is_active': True
                    }
                )
                
                if created:
                    logger.info(f"添加新股票: {stock_code} - {stock_name}")
                
                time.sleep(0.1)  # 避免请求过快
            
            logger.info(f"股票列表更新完成，共处理 {stock_list.shape[0]} 只股票")
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
    
    def fetch_daily_data(self, start_date=None, end_date=None):
        """获取日线数据"""
        if not start_date:
            start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        if not end_date:
            end_date = datetime.now().strftime('%Y%m%d')
        
        logger.info(f"开始获取日线数据: {start_date} 到 {end_date}")
        
        # 获取所有活跃股票
        stocks = Stock.objects.filter(is_active=True)
        
        for stock in stocks:
            try:
                logger.info(f"获取 {stock.stock_code} 的日线数据...")
                
                # 使用AKShare获取历史数据
                df = ak.stock_zh_a_hist(
                    symbol=stock.stock_code,
                    period="daily",
                    start_date=start_date.replace('-', ''),
                    end_date=end_date.replace('-', ''),
                    adjust=""
                )
                
                if df.empty:
                    continue
                
                # 处理数据
                for _, row in df.iterrows():
                    trade_date = pd.to_datetime(row['日期']).date()
                    
                    # 计算涨跌幅和涨跌额
                    change_rate = row.get('涨跌幅', 0)
                    change_amount = row.get('涨跌额', 0)
                    
                    # 创建或更新日线数据
                    daily_data, created = StockDailyData.objects.get_or_create(
                        stock_code=stock.stock_code,
                        trade_date=trade_date,
                        defaults={
                            'open_price': row['开盘'],
                            'close_price': row['收盘'],
                            'high_price': row['最高'],
                            'low_price': row['最低'],
                            'volume': row['成交量'],
                            'amount': row['成交额'],
                            'change_rate': change_rate,
                            'change_amount': change_amount,
                            'turnover_rate': row.get('换手率', 0)
                        }
                    )
                
                logger.info(f"{stock.stock_code} 日线数据更新完成")
                time.sleep(self.sleep_time)
                
            except Exception as e:
                logger.error(f"获取 {stock.stock_code} 日线数据失败: {e}")
                continue
    
    def fetch_minute_data(self, stock_codes=None, period='1'):
        """获取分钟数据"""
        if not stock_codes:
            # 获取前50只活跃股票作为示例
            stocks = Stock.objects.filter(is_active=True)[:50]
            stock_codes = [stock.stock_code for stock in stocks]
        
        logger.info(f"开始获取分钟数据，股票数量: {len(stock_codes)}")
        
        for stock_code in stock_codes:
            try:
                logger.info(f"获取 {stock_code} 的分钟数据...")
                
                # 使用AKShare获取分钟数据
                df = ak.stock_zh_a_hist_min_em(
                    symbol=stock_code,
                    period=period,
                    adjust=""
                )
                
                if df.empty:
                    continue
                
                # 只保留最近的数据
                df = df.tail(1000)  # 最近1000条分钟数据
                
                for _, row in df.iterrows():
                    datetime_str = row['时间']
                    datetime_obj = pd.to_datetime(datetime_str)
                    
                    # 判断数据类型（当日为实时数据）
                    data_type = 2 if datetime_obj.date() == datetime.now().date() else 1
                    
                    # 创建或更新分钟数据
                    minute_data, created = StockMinuteData.objects.get_or_create(
                        stock_code=stock_code,
                        datetime=datetime_obj,
                        defaults={
                            'open_price': row['开盘'],
                            'close_price': row['收盘'],
                            'high_price': row['最高'],
                            'low_price': row['最低'],
                            'volume': row['成交量'],
                            'amount': row['成交额'],
                            'data_type': data_type
                        }
                    )
                
                logger.info(f"{stock_code} 分钟数据更新完成")
                time.sleep(self.sleep_time)
                
            except Exception as e:
                logger.error(f"获取 {stock_code} 分钟数据失败: {e}")
                continue
    
    def fetch_hs300_data(self):
        """获取沪深300指数数据"""
        logger.info("开始获取沪深300指数数据...")
        
        try:
            # 获取沪深300日线数据
            df_daily = ak.stock_zh_index_daily(symbol="sh000300")
            
            if not df_daily.empty:
                df_daily = df_daily.tail(365)  # 最近一年数据
                
                for _, row in df_daily.iterrows():
                    trade_date = pd.to_datetime(row['date']).date()
                    
                    hs300_data, created = HS300Data.objects.get_or_create(
                        trade_date=trade_date,
                        data_type=1,  # 日线
                        defaults={
                            'close_price': row['close'],
                            'change_rate': row.get('pct_chg', 0)
                        }
                    )
            
            # 获取沪深300分钟数据
            df_minute = ak.stock_zh_index_hist_min_em(symbol="000300", period="1")
            
            if not df_minute.empty:
                df_minute = df_minute.tail(1000)  # 最近1000条分钟数据
                
                for _, row in df_minute.iterrows():
                    datetime_obj = pd.to_datetime(row['时间'])
                    trade_date = datetime_obj.date()
                    
                    hs300_data, created = HS300Data.objects.get_or_create(
                        trade_date=trade_date,
                        datetime=datetime_obj,
                        data_type=2,  # 分钟线
                        defaults={
                            'close_price': row['收盘'],
                            'change_rate': 0  # 分钟数据暂不计算涨跌幅
                        }
                    )
            
            logger.info("沪深300指数数据更新完成")
            
        except Exception as e:
            logger.error(f"获取沪深300数据失败: {e}")

def main():
    """主函数"""
    fetcher = StockDataFetcher()
    
    print("股票数据爬取脚本")
    print("1. 获取股票列表")
    print("2. 获取日线数据")
    print("3. 获取分钟数据")
    print("4. 获取沪深300数据")
    print("5. 全部执行")
    
    choice = input("请选择要执行的操作 (1-5): ")
    
    if choice == '1':
        fetcher.fetch_stock_list()
    elif choice == '2':
        fetcher.fetch_daily_data()
    elif choice == '3':
        fetcher.fetch_minute_data()
    elif choice == '4':
        fetcher.fetch_hs300_data()
    elif choice == '5':
        fetcher.fetch_stock_list()
        fetcher.fetch_daily_data()
        fetcher.fetch_minute_data()
        fetcher.fetch_hs300_data()
    else:
        print("无效选择")

if __name__ == '__main__':
    main()
