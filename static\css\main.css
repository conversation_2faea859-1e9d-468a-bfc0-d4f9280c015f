/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', <PERSON><PERSON>, sans-serif;
    background-color: #f5f5f5;
}

#app {
    height: 100vh;
}

/* 头部样式 */
.header {
    background-color: #409EFF;
    color: white;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    height: 60px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header-left h2 {
    margin: 0;
    font-size: 20px;
}

.header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 40px;
}

.search-input {
    width: 100%;
}

.search-input .el-input__inner {
    background-color: rgba(255,255,255,0.9);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.user-info {
    font-size: 14px;
}

.user-dropdown {
    color: white;
    cursor: pointer;
    padding: 8px 12px;
    border-radius: 4px;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    gap: 5px;
}

.user-dropdown:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 登录注册对话框美化 */
.auth-dialog .el-dialog {
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
}

.auth-dialog .el-dialog__body {
    padding: 0;
}

.auth-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    position: relative;
    overflow: hidden;
}

.auth-container::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(180deg); }
}

.auth-header {
    text-align: center;
    padding: 40px 40px 20px;
    position: relative;
    z-index: 1;
}

.auth-logo {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.auth-logo i {
    font-size: 36px;
    color: white;
}

.auth-title {
    font-size: 28px;
    font-weight: 600;
    margin: 0 0 8px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-subtitle {
    font-size: 16px;
    opacity: 0.9;
    margin: 0;
    font-weight: 300;
}

.auth-form {
    background: white;
    padding: 40px;
    margin: 0;
    position: relative;
    z-index: 1;
}

.auth-form .el-form-item {
    margin-bottom: 24px;
}

.auth-form .el-input__inner {
    border: 2px solid #e8ecf4;
    border-radius: 12px;
    padding: 12px 15px 12px 45px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: #f8f9fb;
}

.auth-form .el-input__inner:focus {
    border-color: #667eea;
    background: white;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.auth-form .el-input__prefix {
    left: 15px;
    color: #8b9dc3;
}

.auth-button {
    width: 100%;
    height: 50px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    transition: all 0.3s ease;
    margin-top: 10px;
}

.auth-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.auth-footer {
    background: white;
    padding: 20px 40px 30px;
    text-align: center;
    color: #666;
    font-size: 14px;
    position: relative;
    z-index: 1;
}

.auth-link {
    color: #667eea !important;
    font-weight: 600;
    padding: 0 !important;
    margin-left: 5px;
}

.auth-link:hover {
    color: #764ba2 !important;
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .auth-dialog .el-dialog {
        width: 90% !important;
        margin: 5vh auto;
    }

    .auth-header {
        padding: 30px 20px 15px;
    }

    .auth-form {
        padding: 30px 20px;
    }

    .auth-footer {
        padding: 15px 20px 25px;
    }
}

/* 主容器样式 */
.main-container {
    height: calc(100vh - 60px);
}

/* 侧边栏样式 */
.sidebar {
    background-color: #304156;
    overflow: hidden;
}

.sidebar-menu {
    border: none;
    background-color: #304156;
}

.sidebar-menu .el-menu-item {
    color: #bfcbd9;
    border-bottom: 1px solid #263445;
}

.sidebar-menu .el-menu-item:hover {
    background-color: #263445;
    color: #409EFF;
}

.sidebar-menu .el-menu-item.is-active {
    background-color: #409EFF;
    color: white;
}

/* 主内容区样式 */
.main-content {
    background-color: #f5f5f5;
    padding: 20px;
    overflow-y: auto;
}

/* 卡片样式 */
.content-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    padding: 20px 20px 0 20px;
    border-bottom: 1px solid #ebeef5;
    margin-bottom: 20px;
}

.card-header h3 {
    margin: 0 0 15px 0;
    color: #303133;
    font-size: 18px;
}

.card-body {
    padding: 0 20px 20px 20px;
}

/* 股票列表样式 */
.stock-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid #ebeef5;
    cursor: pointer;
    transition: background-color 0.3s;
}

.stock-item:hover {
    background-color: #f5f7fa;
}

.stock-item:last-child {
    border-bottom: none;
}

.stock-info {
    flex: 1;
}

.stock-code {
    font-weight: bold;
    color: #303133;
    font-size: 16px;
}

.stock-name {
    color: #606266;
    font-size: 14px;
    margin-top: 4px;
}

.stock-price {
    text-align: right;
    min-width: 120px;
}

.current-price {
    font-size: 18px;
    font-weight: bold;
}

.price-change {
    font-size: 14px;
    margin-top: 4px;
}

.price-up {
    color: #f56c6c;
}

.price-down {
    color: #67c23a;
}

.price-flat {
    color: #909399;
}

/* 图表容器样式 */
.chart-container {
    width: 100%;
    height: 400px;
    margin: 20px 0;
}

/* 交易表单样式 */
.trading-form {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-col {
    flex: 1;
}

/* 数据表格样式 */
.data-table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.table-header {
    background-color: #f5f7fa;
    padding: 15px 20px;
    border-bottom: 1px solid #ebeef5;
}

.table-header h4 {
    margin: 0;
    color: #303133;
}

/* 统计卡片样式 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background: white;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    text-align: center;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #409EFF;
    margin-bottom: 8px;
}

.stat-label {
    color: #606266;
    font-size: 14px;
}

/* 按钮组样式 */
.button-group {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .header {
        flex-direction: column;
        height: auto;
        padding: 10px;
    }
    
    .header-center {
        margin: 10px 0;
        max-width: 100%;
    }
    
    .main-container {
        height: calc(100vh - 100px);
    }
    
    .sidebar {
        width: 100% !important;
        height: auto;
    }
    
    .form-row {
        flex-direction: column;
        gap: 10px;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
}

/* 加载动画 */
.loading {
    text-align: center;
    padding: 40px;
    color: #909399;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #909399;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 20px;
    display: block;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 策略详情对话框样式 */
.detail-card {
    margin-bottom: 20px;
}

.profit-item {
    text-align: center;
    padding: 10px;
}

.profit-label {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.profit-value {
    font-size: 20px;
    font-weight: bold;
}

.profit-positive {
    color: #67C23A;
}

.profit-negative {
    color: #F56C6C;
}
