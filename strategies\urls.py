from django.urls import path
from . import views

app_name = 'strategies'

urlpatterns = [
    # 创建策略
    path('grid/create/', views.CreateGridStrategyView.as_view(), name='create_grid_strategy'),
    path('martin/create/', views.CreateMartinStrategyView.as_view(), name='create_martin_strategy'),
    
    # 策略管理
    path('running/', views.RunningStrategiesView.as_view(), name='running_strategies'),
    path('history/', views.HistoryStrategiesView.as_view(), name='history_strategies'),
    path('<int:pk>/', views.StrategyDetailView.as_view(), name='strategy_detail'),
    path('<int:strategy_id>/stop/', views.StopStrategyView.as_view(), name='stop_strategy'),
    path('<int:strategy_id>/update-name/', views.update_strategy_name, name='update_strategy_name'),
    
    # 策略执行记录
    path('<int:strategy_id>/executions/', views.StrategyExecutionsView.as_view(), name='strategy_executions'),
    
    # 策略统计
    path('summary/', views.strategy_summary, name='strategy_summary'),
]
