from django.urls import path
from . import views

app_name = 'stocks'

urlpatterns = [
    # 沪深300数据 (必须在通用股票路由之前)
    path('hs300/chart/', views.HS300ChartDataView.as_view(), name='hs300_chart'),
    path('hs300/test/', views.HS300TestView.as_view(), name='hs300_test'),

    # 收藏功能
    path('favorites/', views.UserFavoriteListView.as_view(), name='favorite_list'),
    path('favorites/add/', views.AddFavoriteView.as_view(), name='add_favorite'),
    path('favorites/<str:stock_code>/remove/', views.RemoveFavoriteView.as_view(), name='remove_favorite'),

    # 股票数据 (通用路由放在最后)
    path('ranking/', views.StockRankingView.as_view(), name='stock_ranking'),
    path('search/', views.StockSearchView.as_view(), name='stock_search'),
    path('<str:stock_code>/', views.StockDetailView.as_view(), name='stock_detail'),
    path('<str:stock_code>/chart/', views.StockChartDataView.as_view(), name='stock_chart'),
    path('<str:stock_code>/changes/', views.StockPeriodChangeView.as_view(), name='stock_changes'),
    path('<str:stock_code>/price/', views.get_current_price, name='current_price'),
]
