#!/usr/bin/env python
"""
完整的股票数据爬取系统
包含日线数据、历史分钟数据、当日实时分钟数据
"""

import os
import sys
import django
from datetime import datetime, timedelta, date
import pandas as pd
import akshare as ak
import tushare as ts
import time
import logging
from decimal import Decimal
import pymysql

# 添加Django项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock_management.settings')
django.setup()

from stocks.models import Stock, StockDailyData, StockMinuteData, HS300Data

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('stock_data_fetch.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Tushare配置
TUSHARE_TOKEN = '4663a4811b61b7ae71fbee8e0e84e2600c8d03530ce45ace9f58c3cd'
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

class CompleteStockDataFetcher:
    def __init__(self):
        self.sleep_time = 0.3  # 请求间隔，避免被限制
        self.batch_size = 50   # 批量处理大小
        
    def get_stock_list(self):
        """获取A股股票列表"""
        logger.info("开始获取A股股票列表...")
        
        try:
            # 使用AKShare获取股票基本信息
            stock_info = ak.stock_info_a_code_name()
            logger.info(f"获取到 {len(stock_info)} 只股票")
            
            # 批量创建股票记录
            stocks_to_create = []
            for _, row in stock_info.iterrows():
                code = row['code']
                name = row['name']
                
                # 判断市场
                if code.startswith('6'):
                    market = 'SH'
                elif code.startswith(('0', '3')):
                    market = 'SZ'
                else:
                    continue
                
                # 检查是否已存在
                if not Stock.objects.filter(stock_code=code).exists():
                    stocks_to_create.append(Stock(
                        stock_code=code,
                        stock_name=name,
                        market=market,
                        is_active=True
                    ))
            
            # 批量创建
            if stocks_to_create:
                Stock.objects.bulk_create(stocks_to_create, batch_size=self.batch_size)
                logger.info(f"新增 {len(stocks_to_create)} 只股票")
            
            return Stock.objects.filter(is_active=True).count()
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return 0
    
    def fetch_daily_data(self, days_back=365):
        """获取日线数据（历史数据，不包含当日）"""
        logger.info(f"开始获取过去 {days_back} 天的日线数据...")
        
        # 计算日期范围（不包含今天）
        end_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y%m%d')
        
        logger.info(f"数据范围: {start_date} 到 {end_date}")
        
        # 获取所有活跃股票
        stocks = Stock.objects.filter(is_active=True)
        total_stocks = stocks.count()
        
        success_count = 0
        error_count = 0
        
        for i, stock in enumerate(stocks, 1):
            try:
                logger.info(f"[{i}/{total_stocks}] 获取 {stock.stock_code} - {stock.stock_name} 的日线数据")
                
                # 使用AKShare获取历史日线数据
                df = ak.stock_zh_a_hist(
                    symbol=stock.stock_code,
                    period="daily",
                    start_date=start_date,
                    end_date=end_date,
                    adjust=""
                )
                
                if df.empty:
                    logger.warning(f"{stock.stock_code} 无数据")
                    continue
                
                # 批量创建日线数据
                daily_data_to_create = []
                for _, row in df.iterrows():
                    trade_date = pd.to_datetime(row['日期']).date()
                    
                    # 检查是否已存在
                    if StockDailyData.objects.filter(
                        stock_code=stock.stock_code, 
                        trade_date=trade_date
                    ).exists():
                        continue
                    
                    # 计算涨跌幅和涨跌额
                    change_rate = row.get('涨跌幅', 0)
                    change_amount = row.get('涨跌额', 0)
                    
                    daily_data_to_create.append(StockDailyData(
                        stock_code=stock.stock_code,
                        trade_date=trade_date,
                        open_price=Decimal(str(row['开盘'])),
                        close_price=Decimal(str(row['收盘'])),
                        high_price=Decimal(str(row['最高'])),
                        low_price=Decimal(str(row['最低'])),
                        volume=int(row['成交量']) if pd.notna(row['成交量']) else 0,
                        amount=Decimal(str(row['成交额'])) if pd.notna(row['成交额']) else Decimal('0'),
                        change_rate=Decimal(str(change_rate)) if pd.notna(change_rate) else Decimal('0'),
                        change_amount=Decimal(str(change_amount)) if pd.notna(change_amount) else Decimal('0'),
                        turnover_rate=Decimal(str(row.get('换手率', 0))) if pd.notna(row.get('换手率', 0)) else Decimal('0')
                    ))
                
                # 批量插入
                if daily_data_to_create:
                    StockDailyData.objects.bulk_create(daily_data_to_create, batch_size=self.batch_size)
                    logger.info(f"{stock.stock_code} 新增 {len(daily_data_to_create)} 条日线数据")
                
                success_count += 1
                time.sleep(self.sleep_time)
                
            except Exception as e:
                error_count += 1
                logger.error(f"获取 {stock.stock_code} 日线数据失败: {e}")
                continue
        
        logger.info(f"日线数据获取完成: 成功 {success_count}, 失败 {error_count}")
        return success_count
    
    def fetch_historical_minute_data(self, days_back=30):
        """获取历史分钟数据（不包含当日）"""
        logger.info(f"开始获取过去 {days_back} 天的历史分钟数据...")
        
        # 获取主要股票（前100只）
        stocks = Stock.objects.filter(is_active=True)[:100]
        total_stocks = stocks.count()
        
        success_count = 0
        error_count = 0
        
        for i, stock in enumerate(stocks, 1):
            try:
                logger.info(f"[{i}/{total_stocks}] 获取 {stock.stock_code} 的历史分钟数据")
                
                # 获取分钟数据（AKShare的分钟数据通常包含最近几天）
                df = ak.stock_zh_a_hist_min_em(
                    symbol=stock.stock_code,
                    period="1",  # 1分钟
                    adjust=""
                )
                
                if df.empty:
                    logger.warning(f"{stock.stock_code} 无分钟数据")
                    continue
                
                # 过滤掉今天的数据（历史数据不包含当日）
                today = datetime.now().date()
                df['时间'] = pd.to_datetime(df['时间'])
                df = df[df['时间'].dt.date < today]
                
                if df.empty:
                    continue
                
                # 只保留最近的数据
                df = df.tail(1000)
                
                # 批量创建分钟数据
                minute_data_to_create = []
                for _, row in df.iterrows():
                    datetime_obj = row['时间']
                    
                    # 检查是否已存在
                    if StockMinuteData.objects.filter(
                        stock_code=stock.stock_code,
                        datetime=datetime_obj
                    ).exists():
                        continue
                    
                    minute_data_to_create.append(StockMinuteData(
                        stock_code=stock.stock_code,
                        datetime=datetime_obj,
                        open_price=Decimal(str(row['开盘'])),
                        close_price=Decimal(str(row['收盘'])),
                        high_price=Decimal(str(row['最高'])),
                        low_price=Decimal(str(row['最低'])),
                        volume=int(row['成交量']) if pd.notna(row['成交量']) else 0,
                        amount=Decimal(str(row['成交额'])) if pd.notna(row['成交额']) else Decimal('0'),
                        data_type=1  # 历史分钟数据
                    ))
                
                # 批量插入
                if minute_data_to_create:
                    StockMinuteData.objects.bulk_create(minute_data_to_create, batch_size=self.batch_size)
                    logger.info(f"{stock.stock_code} 新增 {len(minute_data_to_create)} 条历史分钟数据")
                
                success_count += 1
                time.sleep(self.sleep_time)
                
            except Exception as e:
                error_count += 1
                logger.error(f"获取 {stock.stock_code} 历史分钟数据失败: {e}")
                continue
        
        logger.info(f"历史分钟数据获取完成: 成功 {success_count}, 失败 {error_count}")
        return success_count
    
    def fetch_realtime_minute_data(self):
        """获取当日实时分钟数据"""
        logger.info("开始获取当日实时分钟数据...")
        
        # 获取主要股票（前50只）
        stocks = Stock.objects.filter(is_active=True)[:50]
        total_stocks = stocks.count()
        
        success_count = 0
        error_count = 0
        today = datetime.now().date()
        
        for i, stock in enumerate(stocks, 1):
            try:
                logger.info(f"[{i}/{total_stocks}] 获取 {stock.stock_code} 的当日分钟数据")
                
                # 获取当日分钟数据
                df = ak.stock_zh_a_hist_min_em(
                    symbol=stock.stock_code,
                    period="1",
                    adjust=""
                )
                
                if df.empty:
                    continue
                
                # 只保留今天的数据
                df['时间'] = pd.to_datetime(df['时间'])
                df = df[df['时间'].dt.date == today]
                
                if df.empty:
                    continue
                
                # 批量创建/更新当日分钟数据
                minute_data_to_create = []
                minute_data_to_update = []
                
                for _, row in df.iterrows():
                    datetime_obj = row['时间']
                    
                    # 检查是否已存在
                    existing = StockMinuteData.objects.filter(
                        stock_code=stock.stock_code,
                        datetime=datetime_obj
                    ).first()
                    
                    if existing:
                        # 更新现有数据
                        existing.close_price = Decimal(str(row['收盘']))
                        existing.high_price = Decimal(str(row['最高']))
                        existing.low_price = Decimal(str(row['最低']))
                        existing.volume = int(row['成交量']) if pd.notna(row['成交量']) else 0
                        existing.amount = Decimal(str(row['成交额'])) if pd.notna(row['成交额']) else Decimal('0')
                        existing.data_type = 2  # 当日实时数据
                        minute_data_to_update.append(existing)
                    else:
                        # 创建新数据
                        minute_data_to_create.append(StockMinuteData(
                            stock_code=stock.stock_code,
                            datetime=datetime_obj,
                            open_price=Decimal(str(row['开盘'])),
                            close_price=Decimal(str(row['收盘'])),
                            high_price=Decimal(str(row['最高'])),
                            low_price=Decimal(str(row['最低'])),
                            volume=int(row['成交量']) if pd.notna(row['成交量']) else 0,
                            amount=Decimal(str(row['成交额'])) if pd.notna(row['成交额']) else Decimal('0'),
                            data_type=2  # 当日实时数据
                        ))
                
                # 批量操作
                if minute_data_to_create:
                    StockMinuteData.objects.bulk_create(minute_data_to_create, batch_size=self.batch_size)
                
                if minute_data_to_update:
                    StockMinuteData.objects.bulk_update(
                        minute_data_to_update,
                        ['close_price', 'high_price', 'low_price', 'volume', 'amount', 'data_type'],
                        batch_size=self.batch_size
                    )
                
                total_processed = len(minute_data_to_create) + len(minute_data_to_update)
                if total_processed > 0:
                    logger.info(f"{stock.stock_code} 处理 {total_processed} 条当日分钟数据")
                
                success_count += 1
                time.sleep(self.sleep_time)
                
            except Exception as e:
                error_count += 1
                logger.error(f"获取 {stock.stock_code} 当日分钟数据失败: {e}")
                continue
        
        logger.info(f"当日分钟数据获取完成: 成功 {success_count}, 失败 {error_count}")
        return success_count
    
    def fetch_hs300_data(self):
        """获取沪深300指数数据"""
        logger.info("开始获取沪深300指数数据...")
        
        try:
            # 获取沪深300日线数据
            df_daily = ak.stock_zh_index_daily(symbol="sh000300")
            
            if not df_daily.empty:
                # 只保留最近一年的数据
                df_daily = df_daily.tail(365)
                
                daily_data_to_create = []
                for _, row in df_daily.iterrows():
                    trade_date = pd.to_datetime(row['date']).date()
                    
                    if not HS300Data.objects.filter(trade_date=trade_date, data_type=1).exists():
                        daily_data_to_create.append(HS300Data(
                            trade_date=trade_date,
                            close_price=Decimal(str(row['close'])),
                            change_rate=Decimal(str(row.get('pct_chg', 0))),
                            data_type=1  # 日线
                        ))
                
                if daily_data_to_create:
                    HS300Data.objects.bulk_create(daily_data_to_create, batch_size=self.batch_size)
                    logger.info(f"新增 {len(daily_data_to_create)} 条沪深300日线数据")
            
            # 获取沪深300分钟数据
            df_minute = ak.stock_zh_index_hist_min_em(symbol="000300", period="1")
            
            if not df_minute.empty:
                # 只保留最近的数据
                df_minute = df_minute.tail(1000)
                
                minute_data_to_create = []
                for _, row in df_minute.iterrows():
                    datetime_obj = pd.to_datetime(row['时间'])
                    trade_date = datetime_obj.date()
                    
                    if not HS300Data.objects.filter(
                        trade_date=trade_date, 
                        datetime=datetime_obj, 
                        data_type=2
                    ).exists():
                        minute_data_to_create.append(HS300Data(
                            trade_date=trade_date,
                            datetime=datetime_obj,
                            close_price=Decimal(str(row['收盘'])),
                            change_rate=Decimal('0'),  # 分钟数据暂不计算涨跌幅
                            data_type=2  # 分钟线
                        ))
                
                if minute_data_to_create:
                    HS300Data.objects.bulk_create(minute_data_to_create, batch_size=self.batch_size)
                    logger.info(f"新增 {len(minute_data_to_create)} 条沪深300分钟数据")
            
            logger.info("沪深300指数数据获取完成")
            return True
            
        except Exception as e:
            logger.error(f"获取沪深300数据失败: {e}")
            return False

def main():
    """主函数"""
    fetcher = CompleteStockDataFetcher()
    
    print("=" * 60)
    print("股票数据完整爬取系统")
    print("=" * 60)
    print("1. 获取股票列表")
    print("2. 获取日线数据（历史数据，不含当日）")
    print("3. 获取历史分钟数据（不含当日）")
    print("4. 获取当日实时分钟数据")
    print("5. 获取沪深300指数数据")
    print("6. 执行完整数据爬取（推荐）")
    print("0. 退出")
    print("=" * 60)
    
    while True:
        choice = input("请选择要执行的操作 (0-6): ").strip()
        
        if choice == '0':
            print("退出程序")
            break
        elif choice == '1':
            count = fetcher.get_stock_list()
            print(f"股票列表更新完成，共 {count} 只股票")
        elif choice == '2':
            count = fetcher.fetch_daily_data()
            print(f"日线数据获取完成，成功处理 {count} 只股票")
        elif choice == '3':
            count = fetcher.fetch_historical_minute_data()
            print(f"历史分钟数据获取完成，成功处理 {count} 只股票")
        elif choice == '4':
            count = fetcher.fetch_realtime_minute_data()
            print(f"当日分钟数据获取完成，成功处理 {count} 只股票")
        elif choice == '5':
            success = fetcher.fetch_hs300_data()
            print("沪深300数据获取" + ("成功" if success else "失败"))
        elif choice == '6':
            print("开始执行完整数据爬取...")
            print("\n步骤1: 获取股票列表")
            stock_count = fetcher.get_stock_list()
            print(f"✓ 股票列表: {stock_count} 只")
            
            print("\n步骤2: 获取日线数据")
            daily_count = fetcher.fetch_daily_data()
            print(f"✓ 日线数据: {daily_count} 只股票")
            
            print("\n步骤3: 获取历史分钟数据")
            hist_minute_count = fetcher.fetch_historical_minute_data()
            print(f"✓ 历史分钟数据: {hist_minute_count} 只股票")
            
            print("\n步骤4: 获取当日分钟数据")
            real_minute_count = fetcher.fetch_realtime_minute_data()
            print(f"✓ 当日分钟数据: {real_minute_count} 只股票")
            
            print("\n步骤5: 获取沪深300数据")
            hs300_success = fetcher.fetch_hs300_data()
            print(f"✓ 沪深300数据: {'成功' if hs300_success else '失败'}")
            
            print("\n" + "=" * 60)
            print("完整数据爬取完成！")
            print("=" * 60)
            break
        else:
            print("无效选择，请重新输入")

if __name__ == '__main__':
    main()
