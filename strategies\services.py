from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from .models import TradingStrategy, StrategyExecution, GridLevel
from trading.services import TradingService
from stocks.services import StockDataService
import logging

logger = logging.getLogger(__name__)


class StrategyService:
    """策略服务"""
    
    def __init__(self):
        self.trading_service = TradingService()
        self.stock_service = StockDataService()
    
    def create_grid_strategy(self, user, strategy_name, stock_code, min_price, max_price, grid_count, investment):
        """创建网格交易策略"""
        try:
            with transaction.atomic():
                # 验证参数
                if min_price >= max_price:
                    return {'success': False, 'message': '最低价必须小于最高价'}
                
                if grid_count < 2:
                    return {'success': False, 'message': '网格数量至少为2'}
                
                if investment <= 0:
                    return {'success': False, 'message': '投资额必须大于0'}
                
                # 检查资金是否充足
                if user.current_capital < investment:
                    return {'success': False, 'message': '资金不足'}
                
                # 创建策略
                strategy = TradingStrategy.objects.create(
                    user=user,
                    strategy_name=strategy_name,
                    strategy_type=1,  # 网格交易
                    stock_code=stock_code,
                    grid_min_price=min_price,
                    grid_max_price=max_price,
                    grid_count=grid_count,
                    grid_investment=investment,
                    total_investment=investment
                )
                
                # 创建网格层级
                self.create_grid_levels(strategy)
                
                # 扣除投资资金
                user.update_capital(-investment)
                
                return {'success': True, 'strategy': strategy}
                
        except Exception as e:
            logger.error(f"创建网格策略失败: {e}")
            return {'success': False, 'message': str(e)}
    
    def create_grid_levels(self, strategy):
        """创建网格层级"""
        try:
            price_step = (strategy.grid_max_price - strategy.grid_min_price) / (strategy.grid_count - 1)
            quantity_per_level = int(strategy.grid_investment / strategy.grid_count / strategy.grid_min_price)
            
            for i in range(strategy.grid_count):
                level_price = strategy.grid_min_price + (price_step * i)
                
                GridLevel.objects.create(
                    strategy=strategy,
                    level=i + 1,
                    price=level_price,
                    quantity=quantity_per_level
                )
            
            # 初始化网格订单
            self.initialize_grid_orders(strategy)
            
        except Exception as e:
            logger.error(f"创建网格层级失败: {e}")
    
    def initialize_grid_orders(self, strategy):
        """初始化网格订单"""
        try:
            current_price = self.stock_service.get_current_price(strategy.stock_code)
            if not current_price:
                return
            
            levels = GridLevel.objects.filter(strategy=strategy).order_by('level')
            
            for level in levels:
                # 如果当前价格高于网格价格，放置买单
                if current_price > level.price:
                    result = self.trading_service.create_order(
                        user=strategy.user,
                        stock_code=strategy.stock_code,
                        order_type=1,  # 买入
                        order_price=level.price,
                        order_quantity=level.quantity,
                        strategy_id=strategy.id
                    )
                    if result['success']:
                        level.buy_order_id = result['order'].id
                        level.save()
                
                # 如果当前价格低于网格价格，放置卖单（需要有持仓）
                elif current_price < level.price:
                    # 这里简化处理，实际应该检查持仓
                    pass
            
        except Exception as e:
            logger.error(f"初始化网格订单失败: {e}")
    
    def create_martin_strategy(self, user, strategy_name, stock_code, trigger_rate, profit_target, 
                             initial_amount, add_amount, max_add_times):
        """创建马丁格尔策略"""
        try:
            with transaction.atomic():
                # 验证参数
                if trigger_rate <= 0 or trigger_rate >= 100:
                    return {'success': False, 'message': '触发跌幅比例必须在0-100之间'}
                
                if profit_target <= 0:
                    return {'success': False, 'message': '止盈目标必须大于0'}
                
                if initial_amount <= 0 or add_amount <= 0:
                    return {'success': False, 'message': '投资金额必须大于0'}
                
                if max_add_times < 1:
                    return {'success': False, 'message': '最大加仓次数至少为1'}
                
                # 计算总投资额
                total_investment = initial_amount + (add_amount * max_add_times)
                
                # 检查资金是否充足
                if user.current_capital < total_investment:
                    return {'success': False, 'message': '资金不足'}
                
                # 创建策略
                strategy = TradingStrategy.objects.create(
                    user=user,
                    strategy_name=strategy_name,
                    strategy_type=2,  # 马丁格尔
                    stock_code=stock_code,
                    martin_trigger_rate=trigger_rate,
                    martin_profit_target=profit_target,
                    martin_initial_amount=initial_amount,
                    martin_add_amount=add_amount,
                    martin_max_add_times=max_add_times,
                    total_investment=total_investment
                )
                
                # 执行初次买入
                self.execute_martin_initial_buy(strategy)
                
                # 扣除投资资金
                user.update_capital(-total_investment)
                
                return {'success': True, 'strategy': strategy}
                
        except Exception as e:
            logger.error(f"创建马丁格尔策略失败: {e}")
            return {'success': False, 'message': str(e)}
    
    def execute_martin_initial_buy(self, strategy):
        """执行马丁格尔初次买入"""
        try:
            current_price = self.stock_service.get_current_price(strategy.stock_code)
            if not current_price:
                return
            
            quantity = int(strategy.martin_initial_amount / current_price)
            
            result = self.trading_service.create_order(
                user=strategy.user,
                stock_code=strategy.stock_code,
                order_type=1,  # 买入
                order_price=current_price,
                order_quantity=quantity,
                strategy_id=strategy.id
            )
            
            if result['success']:
                # 记录执行
                StrategyExecution.objects.create(
                    strategy=strategy,
                    execution_type=1,
                    trigger_price=current_price,
                    execution_price=current_price,
                    quantity=quantity,
                    amount=strategy.martin_initial_amount,
                    order_id=result['order'].id
                )
            
        except Exception as e:
            logger.error(f"执行马丁格尔初次买入失败: {e}")
    
    def check_martin_triggers(self, strategy):
        """检查马丁格尔触发条件"""
        try:
            current_price = self.stock_service.get_current_price(strategy.stock_code)
            if not current_price:
                return
            
            # 获取最后一次买入价格
            last_execution = StrategyExecution.objects.filter(
                strategy=strategy,
                execution_type=1  # 买入
            ).order_by('-execution_time').first()
            
            if not last_execution:
                return
            
            # 计算跌幅
            drop_rate = ((last_execution.execution_price - current_price) / last_execution.execution_price) * 100
            
            # 检查是否触发加仓
            if (drop_rate >= strategy.martin_trigger_rate and 
                strategy.martin_current_add_times < strategy.martin_max_add_times):
                
                self.execute_martin_add_position(strategy, current_price)
            
            # 检查是否触发止盈
            # 这里需要计算平均成本价和当前价格的差异
            self.check_martin_profit_target(strategy, current_price)
            
        except Exception as e:
            logger.error(f"检查马丁格尔触发条件失败: {e}")
    
    def execute_martin_add_position(self, strategy, current_price):
        """执行马丁格尔加仓"""
        try:
            quantity = int(strategy.martin_add_amount / current_price)
            
            result = self.trading_service.create_order(
                user=strategy.user,
                stock_code=strategy.stock_code,
                order_type=1,  # 买入
                order_price=current_price,
                order_quantity=quantity,
                strategy_id=strategy.id
            )
            
            if result['success']:
                # 更新加仓次数
                strategy.martin_current_add_times += 1
                strategy.save()
                
                # 记录执行
                StrategyExecution.objects.create(
                    strategy=strategy,
                    execution_type=1,
                    trigger_price=current_price,
                    execution_price=current_price,
                    quantity=quantity,
                    amount=strategy.martin_add_amount,
                    order_id=result['order'].id
                )
            
        except Exception as e:
            logger.error(f"执行马丁格尔加仓失败: {e}")
    
    def check_martin_profit_target(self, strategy, current_price):
        """检查马丁格尔止盈目标"""
        try:
            # 获取策略相关的持仓
            from trading.models import UserPosition
            position = UserPosition.objects.filter(
                user=strategy.user,
                stock_code=strategy.stock_code
            ).first()
            
            if not position or position.total_quantity <= 0:
                return
            
            # 计算收益率
            profit_rate = ((current_price - position.avg_cost_price) / position.avg_cost_price) * 100
            
            if profit_rate >= strategy.martin_profit_target:
                self.execute_martin_sell_all(strategy, current_price, position)
            
        except Exception as e:
            logger.error(f"检查马丁格尔止盈目标失败: {e}")
    
    def execute_martin_sell_all(self, strategy, current_price, position):
        """执行马丁格尔全部卖出"""
        try:
            result = self.trading_service.create_order(
                user=strategy.user,
                stock_code=strategy.stock_code,
                order_type=2,  # 卖出
                order_price=current_price,
                order_quantity=position.total_quantity,
                strategy_id=strategy.id
            )
            
            if result['success']:
                # 计算收益
                profit = (current_price - position.avg_cost_price) * position.total_quantity
                
                # 记录执行
                StrategyExecution.objects.create(
                    strategy=strategy,
                    execution_type=2,
                    trigger_price=current_price,
                    execution_price=current_price,
                    quantity=position.total_quantity,
                    amount=current_price * position.total_quantity,
                    profit=profit,
                    order_id=result['order'].id
                )
                
                # 更新策略收益
                strategy.realized_profit += profit
                if strategy.total_investment > 0:
                    strategy.realized_profit_rate = (strategy.realized_profit / strategy.total_investment) * 100
                strategy.status = 3  # 已完成
                strategy.end_time = timezone.now()
                strategy.save()
            
        except Exception as e:
            logger.error(f"执行马丁格尔全部卖出失败: {e}")
    
    def stop_strategy(self, strategy_id, user):
        """停止策略"""
        try:
            strategy = TradingStrategy.objects.get(id=strategy_id, user=user)
            
            if strategy.status != 1:  # 不是运行中
                return {'success': False, 'message': '策略不在运行中'}
            
            # 停止策略
            strategy.stop_strategy()
            
            # 撤销相关的未成交委托
            from trading.models import TradingOrder
            pending_orders = TradingOrder.objects.filter(
                user=user,
                strategy_id=strategy_id,
                order_status__in=[1, 2]  # 待成交、部分成交
            )
            
            for order in pending_orders:
                self.trading_service.cancel_order(order.id, user)
            
            # 如果有持仓，全部卖出
            from trading.models import UserPosition
            position = UserPosition.objects.filter(
                user=user,
                stock_code=strategy.stock_code,
                total_quantity__gt=0
            ).first()

            if position:
                current_price = self.stock_service.get_current_price(strategy.stock_code)
                if current_price:
                    self.trading_service.create_order(
                        user=user,
                        stock_code=strategy.stock_code,
                        order_type=2,  # 卖出
                        order_price=current_price,
                        order_quantity=position.total_quantity,
                        strategy_id=strategy_id
                    )

            return {'success': True, 'message': '策略已停止'}

        except TradingStrategy.DoesNotExist:
            return {'success': False, 'message': '策略不存在'}
        except Exception as e:
            logger.error(f"停止策略失败: {e}")
            return {'success': False, 'message': str(e)}

    def run_strategy_monitor(self):
        """运行策略监控（定时任务调用）"""
        try:
            # 获取所有运行中的策略
            running_strategies = TradingStrategy.objects.filter(status=1)

            for strategy in running_strategies:
                if strategy.strategy_type == 1:  # 网格交易
                    self.monitor_grid_strategy(strategy)
                elif strategy.strategy_type == 2:  # 马丁格尔
                    self.check_martin_triggers(strategy)

        except Exception as e:
            logger.error(f"运行策略监控失败: {e}")

    def monitor_grid_strategy(self, strategy):
        """监控网格策略"""
        try:
            current_price = self.stock_service.get_current_price(strategy.stock_code)
            if not current_price:
                return

            levels = GridLevel.objects.filter(strategy=strategy)

            for level in levels:
                # 检查买单是否需要重新放置
                if not level.is_buy_filled and not level.buy_order_id:
                    if current_price > level.price:
                        result = self.trading_service.create_order(
                            user=strategy.user,
                            stock_code=strategy.stock_code,
                            order_type=1,  # 买入
                            order_price=level.price,
                            order_quantity=level.quantity,
                            strategy_id=strategy.id
                        )
                        if result['success']:
                            level.buy_order_id = result['order'].id
                            level.save()

                # 检查卖单是否需要重新放置
                if not level.is_sell_filled and not level.sell_order_id:
                    if current_price < level.price:
                        # 检查是否有足够持仓
                        from trading.models import UserPosition
                        position = UserPosition.objects.filter(
                            user=strategy.user,
                            stock_code=strategy.stock_code
                        ).first()

                        if position and position.available_quantity >= level.quantity:
                            result = self.trading_service.create_order(
                                user=strategy.user,
                                stock_code=strategy.stock_code,
                                order_type=2,  # 卖出
                                order_price=level.price,
                                order_quantity=level.quantity,
                                strategy_id=strategy.id
                            )
                            if result['success']:
                                level.sell_order_id = result['order'].id
                                level.save()

        except Exception as e:
            logger.error(f"监控网格策略失败: {e}")
