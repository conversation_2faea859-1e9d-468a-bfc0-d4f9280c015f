from django.contrib import admin
from .models import TradingStrategy, StrategyExecution, GridLevel


@admin.register(TradingStrategy)
class TradingStrategyAdmin(admin.ModelAdmin):
    list_display = ('strategy_name', 'user', 'strategy_type', 'stock_code', 'status', 
                   'total_investment', 'realized_profit', 'realized_profit_rate', 'created_at')
    list_filter = ('strategy_type', 'status', 'created_at')
    search_fields = ('strategy_name', 'user__username', 'stock_code')
    ordering = ('-created_at',)


@admin.register(StrategyExecution)
class StrategyExecutionAdmin(admin.ModelAdmin):
    list_display = ('strategy', 'execution_type', 'execution_price', 'quantity', 
                   'amount', 'profit', 'execution_time')
    list_filter = ('execution_type', 'execution_time')
    search_fields = ('strategy__strategy_name', 'strategy__user__username')
    ordering = ('-execution_time',)


@admin.register(GridLevel)
class GridLevelAdmin(admin.ModelAdmin):
    list_display = ('strategy', 'level', 'price', 'quantity', 'is_buy_filled', 'is_sell_filled')
    list_filter = ('is_buy_filled', 'is_sell_filled')
    search_fields = ('strategy__strategy_name', 'strategy__user__username')
    ordering = ('strategy', 'level')
