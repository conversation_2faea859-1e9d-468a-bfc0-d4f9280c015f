from django.db import models
from django.conf import settings
from decimal import Decimal
from django.utils import timezone


class TradingOrder(models.Model):
    """交易委托"""
    ORDER_TYPE_CHOICES = [
        (1, '买入'),
        (2, '卖出'),
    ]
    
    ORDER_STATUS_CHOICES = [
        (1, '待成交'),
        (2, '部分成交'),
        (3, '全部成交'),
        (4, '已撤销'),
    ]
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='用户')
    stock_code = models.CharField('股票代码', max_length=10, db_index=True)
    order_type = models.IntegerField('委托类型', choices=ORDER_TYPE_CHOICES)
    order_price = models.DecimalField('委托价格', max_digits=10, decimal_places=3)
    order_quantity = models.IntegerField('委托数量')
    filled_quantity = models.IntegerField('已成交数量', default=0)
    order_status = models.IntegerField('委托状态', choices=ORDER_STATUS_CHOICES, default=1)
    order_time = models.DateTimeField('委托时间', default=timezone.now)
    filled_time = models.DateTimeField('成交时间', null=True, blank=True)
    commission_fee = models.DecimalField('手续费', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    strategy_id = models.IntegerField('关联策略ID', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'trading_orders'
        verbose_name = '交易委托'
        verbose_name_plural = '交易委托'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['stock_code']),
            models.Index(fields=['order_status']),
            models.Index(fields=['strategy_id']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.stock_code} - {self.get_order_type_display()}"
    
    @property
    def remaining_quantity(self):
        """剩余数量"""
        return self.order_quantity - self.filled_quantity
    
    @property
    def is_completed(self):
        """是否完全成交"""
        return self.filled_quantity >= self.order_quantity


class UserPosition(models.Model):
    """用户持仓"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='用户')
    stock_code = models.CharField('股票代码', max_length=10, db_index=True)
    total_quantity = models.IntegerField('总持仓数量', default=0)
    available_quantity = models.IntegerField('可用数量', default=0)
    avg_cost_price = models.DecimalField('平均成本价', max_digits=10, decimal_places=3)
    total_cost = models.DecimalField('总成本', max_digits=15, decimal_places=2)
    current_price = models.DecimalField('当前价格', max_digits=10, decimal_places=3, null=True, blank=True)
    market_value = models.DecimalField('市值', max_digits=15, decimal_places=2, null=True, blank=True)
    profit_loss = models.DecimalField('盈亏', max_digits=15, decimal_places=2, null=True, blank=True)
    profit_loss_rate = models.DecimalField('盈亏率', max_digits=8, decimal_places=4, null=True, blank=True)
    first_buy_time = models.DateTimeField('首次买入时间', null=True, blank=True)
    last_update_time = models.DateTimeField('最后更新时间', auto_now=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        db_table = 'user_positions'
        verbose_name = '用户持仓'
        verbose_name_plural = '用户持仓'
        unique_together = [['user', 'stock_code']]
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['stock_code']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.stock_code}"
    
    def update_market_value(self, current_price):
        """更新市值和盈亏"""
        self.current_price = current_price
        if self.total_quantity > 0:
            self.market_value = current_price * self.total_quantity
            self.profit_loss = self.market_value - self.total_cost
            if self.total_cost > 0:
                self.profit_loss_rate = (self.profit_loss / self.total_cost) * 100
        else:
            self.market_value = Decimal('0.00')
            self.profit_loss = Decimal('0.00')
            self.profit_loss_rate = Decimal('0.0000')
        self.save()


class TradingRecord(models.Model):
    """交易记录"""
    TRADE_TYPE_CHOICES = [
        (1, '买入'),
        (2, '卖出'),
    ]
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='用户')
    stock_code = models.CharField('股票代码', max_length=10, db_index=True)
    trade_type = models.IntegerField('交易类型', choices=TRADE_TYPE_CHOICES)
    trade_price = models.DecimalField('成交价格', max_digits=10, decimal_places=3)
    trade_quantity = models.IntegerField('成交数量')
    trade_amount = models.DecimalField('成交金额', max_digits=15, decimal_places=2)
    commission_fee = models.DecimalField('手续费', max_digits=10, decimal_places=2)
    stamp_duty = models.DecimalField('印花税', max_digits=10, decimal_places=2, default=Decimal('0.00'))
    total_fee = models.DecimalField('总费用', max_digits=10, decimal_places=2)
    net_amount = models.DecimalField('净金额', max_digits=15, decimal_places=2)
    trade_time = models.DateTimeField('成交时间', default=timezone.now)
    order = models.ForeignKey(TradingOrder, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联委托')
    strategy_id = models.IntegerField('关联策略ID', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        db_table = 'trading_records'
        verbose_name = '交易记录'
        verbose_name_plural = '交易记录'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['stock_code']),
            models.Index(fields=['trade_time']),
            models.Index(fields=['strategy_id']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.stock_code} - {self.get_trade_type_display()}"
    
    def save(self, *args, **kwargs):
        # 计算费用
        self.trade_amount = self.trade_price * self.trade_quantity
        
        # 计算手续费 (买卖都收取)
        self.commission_fee = self.trade_amount * Decimal(str(settings.STOCK_DATA_CONFIG['TRADING_FEE_RATE']))
        
        # 计算印花税 (仅卖出收取)
        if self.trade_type == 2:  # 卖出
            self.stamp_duty = self.trade_amount * Decimal(str(settings.STOCK_DATA_CONFIG['STAMP_DUTY_RATE']))
        else:
            self.stamp_duty = Decimal('0.00')
        
        # 计算总费用
        self.total_fee = self.commission_fee + self.stamp_duty
        
        # 计算净金额
        if self.trade_type == 1:  # 买入
            self.net_amount = self.trade_amount + self.total_fee
        else:  # 卖出
            self.net_amount = self.trade_amount - self.total_fee
        
        super().save(*args, **kwargs)
