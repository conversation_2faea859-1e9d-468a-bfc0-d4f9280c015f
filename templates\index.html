<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>股票池自选管理系统</title>
    {% csrf_token %}
    
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
    <!-- 自定义CSS -->
    <link rel="stylesheet" href="/static/css/main.css">
    
    <!-- Vue.js -->
    <script src="https://unpkg.com/vue@2/dist/vue.js"></script>
    <!-- Element UI JS -->
    <script src="https://unpkg.com/element-ui/lib/index.js"></script>
    <!-- Axios -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <!-- ECharts -->
    <script src="https://unpkg.com/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div id="app">
        <!-- 登录模态框 -->
        <el-dialog :visible.sync="loginDialogVisible" width="450px" :close-on-click-modal="false" class="auth-dialog" :show-close="false">
            <div class="auth-container">
                <!-- 头部 -->
                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="el-icon-s-finance"></i>
                    </div>
                    <h2 class="auth-title">欢迎登录</h2>
                    <p class="auth-subtitle">股票池自选管理系统</p>
                </div>

                <!-- 表单 -->
                <div class="auth-form">
                    <el-form :model="loginForm" :rules="loginRules" ref="loginForm">
                        <el-form-item prop="username">
                            <el-input
                                v-model="loginForm.username"
                                placeholder="请输入用户名"
                                prefix-icon="el-icon-user"
                                size="large">
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="password">
                            <el-input
                                v-model="loginForm.password"
                                type="password"
                                placeholder="请输入密码"
                                prefix-icon="el-icon-lock"
                                size="large"
                                @keyup.enter.native="handleLogin">
                            </el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button
                                type="primary"
                                @click="handleLogin"
                                :loading="loginLoading"
                                size="large"
                                class="auth-button">
                                登录
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 底部 -->
                <div class="auth-footer">
                    <span>还没有账户？</span>
                    <el-button type="text" @click="showRegisterDialog" class="auth-link">立即注册</el-button>
                </div>
            </div>
        </el-dialog>

        <!-- 注册模态框 -->
        <el-dialog :visible.sync="registerDialogVisible" width="450px" class="auth-dialog" :show-close="false">
            <div class="auth-container">
                <!-- 头部 -->
                <div class="auth-header">
                    <div class="auth-logo">
                        <i class="el-icon-user-solid"></i>
                    </div>
                    <h2 class="auth-title">创建账户</h2>
                    <p class="auth-subtitle">加入股票池自选管理系统</p>
                </div>

                <!-- 表单 -->
                <div class="auth-form">
                    <el-form :model="registerForm" :rules="registerRules" ref="registerForm">
                        <el-form-item prop="username">
                            <el-input
                                v-model="registerForm.username"
                                placeholder="请输入用户名"
                                prefix-icon="el-icon-user"
                                size="large">
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="password">
                            <el-input
                                v-model="registerForm.password"
                                type="password"
                                placeholder="请输入密码"
                                prefix-icon="el-icon-lock"
                                size="large">
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="password_confirm">
                            <el-input
                                v-model="registerForm.password_confirm"
                                type="password"
                                placeholder="请再次输入密码"
                                prefix-icon="el-icon-lock"
                                size="large">
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="email">
                            <el-input
                                v-model="registerForm.email"
                                placeholder="请输入邮箱（可选）"
                                prefix-icon="el-icon-message"
                                size="large">
                            </el-input>
                        </el-form-item>
                        <el-form-item prop="real_name">
                            <el-input
                                v-model="registerForm.real_name"
                                placeholder="请输入真实姓名（可选）"
                                prefix-icon="el-icon-s-custom"
                                size="large">
                            </el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button
                                type="primary"
                                @click="handleRegister"
                                :loading="registerLoading"
                                size="large"
                                class="auth-button">
                                注册
                            </el-button>
                        </el-form-item>
                    </el-form>
                </div>

                <!-- 底部 -->
                <div class="auth-footer">
                    <span>已有账户？</span>
                    <el-button type="text" @click="showLoginDialog" class="auth-link">立即登录</el-button>
                </div>
            </div>
        </el-dialog>

        <!-- 主界面 -->
        <div v-if="isLoggedIn">
            <!-- 顶部导航栏 -->
            <el-header class="header">
                <div class="header-left">
                    <h2>股票池自选管理系统</h2>
                </div>
                <div class="header-center">
                    <el-input
                        v-model="searchKeyword"
                        placeholder="请输入股票代码或名称"
                        class="search-input"
                        @keyup.enter.native="searchStock">
                        <el-button slot="append" icon="el-icon-search" @click="searchStock"></el-button>
                    </el-input>
                </div>
                <div class="header-right">
                    <el-dropdown @command="handleUserCommand">
                        <span class="user-dropdown">
                            <i class="el-icon-user"></i>
                            欢迎，{{ currentUser.username }}
                            <i class="el-icon-arrow-down el-icon--right"></i>
                        </span>
                        <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item command="profile">个人中心</el-dropdown-item>
                            <el-dropdown-item command="switch" divided>切换账户</el-dropdown-item>
                            <el-dropdown-item command="logout">退出登录</el-dropdown-item>
                        </el-dropdown-menu>
                    </el-dropdown>
                </div>
            </el-header>

            <!-- 主要内容区域 -->
            <el-container class="main-container">
                <!-- 侧边栏 -->
                <el-aside width="200px" class="sidebar">
                    <el-menu
                        :default-active="activeMenu"
                        class="sidebar-menu"
                        @select="handleMenuSelect">
                        <el-menu-item index="home">
                            <i class="el-icon-s-home"></i>
                            <span slot="title">主界面</span>
                        </el-menu-item>
                        <el-menu-item index="favorites">
                            <i class="el-icon-star-on"></i>
                            <span slot="title">收藏</span>
                        </el-menu-item>
                        <el-menu-item index="trading">
                            <i class="el-icon-s-finance"></i>
                            <span slot="title">模拟交易</span>
                        </el-menu-item>
                        <el-menu-item index="strategies">
                            <i class="el-icon-s-operation"></i>
                            <span slot="title">交易策略</span>
                        </el-menu-item>
                        <el-menu-item index="profile">
                            <i class="el-icon-user"></i>
                            <span slot="title">个人中心</span>
                        </el-menu-item>
                        <el-menu-item v-if="currentUser.is_admin" index="admin">
                            <i class="el-icon-s-tools"></i>
                            <span slot="title">系统管理</span>
                        </el-menu-item>
                    </el-menu>
                </el-aside>

                <!-- 主内容区 -->
                <el-main class="main-content">
                    <!-- 主界面 -->
                    <div v-if="activeMenu === 'home'" class="home-page">
                        <home-component ref="homeComponent"></home-component>
                    </div>

                    <!-- 收藏页面 -->
                    <div v-if="activeMenu === 'favorites' && isLoggedIn" class="favorites-page">
                        <favorites-component ref="favoritesComponent"></favorites-component>
                    </div>

                    <!-- 交易页面 -->
                    <div v-if="activeMenu === 'trading' && isLoggedIn" class="trading-page">
                        <trading-component ref="tradingComponent"></trading-component>
                    </div>

                    <!-- 策略页面 -->
                    <div v-if="activeMenu === 'strategies' && isLoggedIn" class="strategies-page">
                        <strategies-component ref="strategiesComponent"></strategies-component>
                    </div>

                    <!-- 个人中心 -->
                    <div v-if="activeMenu === 'profile' && isLoggedIn" class="profile-page">
                        <profile-component ref="profileComponent"></profile-component>
                    </div>

                    <!-- 管理员页面 -->
                    <div v-if="activeMenu === 'admin' && isLoggedIn && currentUser.is_admin" class="admin-page">
                        <admin-component ref="adminComponent"></admin-component>
                    </div>
                </el-main>
            </el-container>
        </div>

        <!-- 切换账户对话框 -->
        <el-dialog title="切换账户" :visible.sync="switchAccountDialogVisible" width="400px">
            <el-form :model="switchAccountForm" :rules="switchAccountRules" ref="switchAccountForm" label-width="80px">
                <el-form-item label="用户名" prop="username">
                    <el-input v-model="switchAccountForm.username" placeholder="请输入用户名"></el-input>
                </el-form-item>
                <el-form-item label="密码" prop="password">
                    <el-input type="password" v-model="switchAccountForm.password" placeholder="请输入密码" @keyup.enter.native="switchAccount"></el-input>
                </el-form-item>
            </el-form>
            <div slot="footer" class="dialog-footer">
                <el-button @click="switchAccountDialogVisible = false">取消</el-button>
                <el-button type="primary" @click="switchAccount" :loading="switchAccountLoading">切换</el-button>
            </div>
        </el-dialog>
    </div>

    <!-- 引入组件 -->
    <script src="/static/js/components/home.js"></script>
    <script src="/static/js/components/favorites.js"></script>
    <script src="/static/js/components/trading.js"></script>
    <script src="/static/js/components/strategies.js"></script>
    <script src="/static/js/components/profile.js"></script>
    <script src="/static/js/components/admin.js"></script>
    <script src="/static/js/utils/api.js"></script>
    <script src="/static/js/main.js"></script>
</body>
</html>
