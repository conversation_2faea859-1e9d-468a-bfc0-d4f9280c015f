from django.contrib.auth.models import AbstractUser
from django.db import models
from decimal import Decimal


class User(AbstractUser):
    """扩展用户模型"""
    phone = models.CharField('手机号', max_length=20, blank=True, null=True)
    real_name = models.Char<PERSON>ield('真实姓名', max_length=50, blank=True, null=True)
    is_admin = models.BooleanField('是否管理员', default=False)
    
    # 交易相关字段
    initial_capital = models.DecimalField('初始资金', max_digits=15, decimal_places=2, default=Decimal('100000.00'))
    current_capital = models.DecimalField('当前资金', max_digits=15, decimal_places=2, default=Decimal('100000.00'))
    total_profit = models.DecimalField('总收益', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    total_profit_rate = models.DecimalField('总收益率', max_digits=8, decimal_places=4, default=Decimal('0.0000'))
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'users'
        verbose_name = '用户'
        verbose_name_plural = '用户'
    
    def __str__(self):
        return self.username
    
    def update_capital(self, amount):
        """更新资金"""
        self.current_capital += Decimal(str(amount))
        self.total_profit = self.current_capital - self.initial_capital
        if self.initial_capital > 0:
            self.total_profit_rate = (self.total_profit / self.initial_capital) * 100
        self.save()
    
    def get_total_assets(self):
        """获取总资产（资金+持仓市值）"""
        from trading.models import UserPosition
        positions = UserPosition.objects.filter(user=self, total_quantity__gt=0)
        total_market_value = sum(pos.market_value or 0 for pos in positions)
        return self.current_capital + total_market_value
