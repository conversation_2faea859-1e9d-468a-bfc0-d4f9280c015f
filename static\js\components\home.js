// 主界面组件
Vue.component('home-component', {
    template: `
        <div class="home-component">
            <!-- 查询条件 -->
            <div class="content-card">
                <div class="card-header">
                    <h3>股票排行</h3>
                </div>
                <div class="card-body">
                    <div class="button-group">
                        <el-radio-group v-model="selectedPeriod" @change="loadRankingData">
                            <el-radio-button label="1Y">过去一年</el-radio-button>
                            <el-radio-button label="1M">过去一月</el-radio-button>
                        </el-radio-group>
                        <el-button @click="loadRankingData" icon="el-icon-refresh">刷新</el-button>
                    </div>
                </div>
            </div>

            <!-- 股票排行列表 -->
            <div class="content-card">
                <div class="card-header">
                    <h3>涨幅排行榜 ({{ selectedPeriod === '1Y' ? '年度' : '月度' }})</h3>
                </div>
                <div class="card-body">
                    <div v-if="loading" class="loading">
                        <i class="el-icon-loading"></i> 加载中...
                    </div>
                    <div v-else-if="rankingList.length === 0" class="empty-state">
                        <i class="el-icon-document"></i>
                        <p>暂无数据</p>
                    </div>
                    <div v-else>
                        <div v-for="(stock, index) in rankingList" :key="stock.stock_code" 
                             class="stock-item" @click="viewStockDetail(stock.stock_code)">
                            <div class="stock-info">
                                <div class="stock-code">{{ index + 1 }}. {{ stock.stock_code }}</div>
                                <div class="stock-name">{{ stock.stock_name }}</div>
                                <div class="stock-market">{{ stock.market }}</div>
                            </div>
                            <div class="stock-price">
                                <div class="current-price">{{ stock.latest_price | formatNumber(3) }}</div>
                                <div class="price-change" :class="getPriceColorClass(stock.change_rate)">
                                    {{ stock.change_rate > 0 ? '+' : '' }}{{ stock.change_rate | formatPercent }}
                                    ({{ stock.change_amount > 0 ? '+' : '' }}{{ stock.change_amount | formatNumber(3) }})
                                </div>
                            </div>
                            <div class="stock-actions">
                                <el-button size="mini" @click.stop="viewStockDetail(stock.stock_code)">详情</el-button>
                                <el-button size="mini" type="primary" @click.stop="addToFavorites(stock.stock_code)">收藏</el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 搜索结果 -->
            <div v-if="searchResults.length > 0" class="content-card">
                <div class="card-header">
                    <h3>搜索结果</h3>
                </div>
                <div class="card-body">
                    <div v-for="stock in searchResults" :key="stock.stock_code" 
                         class="stock-item" @click="viewStockDetail(stock.stock_code)">
                        <div class="stock-info">
                            <div class="stock-code">{{ stock.stock_code }}</div>
                            <div class="stock-name">{{ stock.stock_name }}</div>
                        </div>
                        <div class="stock-price">
                            <div class="current-price">{{ stock.latest_price | formatNumber(3) }}</div>
                            <div class="price-change" :class="getPriceColorClass(stock.latest_change_rate)">
                                {{ stock.latest_change_rate > 0 ? '+' : '' }}{{ stock.latest_change_rate | formatPercent }}
                            </div>
                        </div>
                        <div class="stock-actions">
                            <el-button size="mini" @click.stop="viewStockDetail(stock.stock_code)">详情</el-button>
                            <el-button size="mini" type="primary" @click.stop="addToFavorites(stock.stock_code)">收藏</el-button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 股票详情对话框 -->
            <el-dialog title="股票详情" :visible.sync="detailDialogVisible" width="80%" :close-on-click-modal="false">
                <stock-detail-component 
                    v-if="selectedStockCode" 
                    :stock-code="selectedStockCode"
                    ref="stockDetailComponent">
                </stock-detail-component>
            </el-dialog>
        </div>
    `,
    
    data() {
        return {
            loading: false,
            selectedPeriod: '1Y',
            rankingList: [],
            searchResults: [],
            detailDialogVisible: false,
            selectedStockCode: null
        };
    },
    
    mounted() {
        this.loadRankingData();
    },
    
    methods: {
        // 加载排行数据
        async loadRankingData() {
            this.loading = true;
            const result = await API.stocks.getRanking(this.selectedPeriod, 20);
            this.loading = false;
            
            if (result.success) {
                this.rankingList = result.data;
            } else {
                this.showError('加载排行数据失败: ' + result.error);
            }
        },
        
        // 搜索股票
        async searchStock(keyword) {
            if (!keyword.trim()) return;
            
            const loading = this.showLoading('搜索中...');
            const result = await API.stocks.search(keyword);
            loading.close();
            
            if (result.success) {
                this.searchResults = result.data;
                if (this.searchResults.length === 0) {
                    this.showWarning('未找到相关股票');
                }
            } else {
                this.showError('搜索失败: ' + result.error);
            }
        },
        
        // 查看股票详情
        viewStockDetail(stockCode) {
            this.selectedStockCode = stockCode;
            this.detailDialogVisible = true;
        },
        
        // 添加到收藏
        async addToFavorites(stockCode) {
            const result = await API.favorites.add(stockCode);
            if (result.success) {
                this.showSuccess('添加收藏成功');
            } else {
                if (result.error.includes('身份认证') || result.error.includes('403')) {
                    this.showError('请先登录后再收藏股票');
                } else {
                    this.showError('添加收藏失败: ' + result.error);
                }
            }
        },
        
        // 获取价格颜色类
        getPriceColorClass(value) {
            return Utils.getPriceColorClass(value);
        },
        
        // 刷新数据
        refreshData() {
            this.loadRankingData();
        }
    }
});

// 股票详情组件
Vue.component('stock-detail-component', {
    props: ['stockCode'],
    template: `
        <div class="stock-detail-component">
            <div v-if="loading" class="loading">
                <i class="el-icon-loading"></i> 加载中...
            </div>
            <div v-else-if="stockDetail">
                <!-- 基本信息 -->
                <div class="stock-basic-info">
                    <h2>{{ stockDetail.stock_name }} ({{ stockDetail.stock_code }})</h2>
                    <div class="price-info">
                        <span class="current-price">{{ stockDetail.latest_price | formatNumber(3) }}</span>
                        <span class="price-change" :class="getPriceColorClass(stockDetail.latest_change_rate)">
                            {{ stockDetail.latest_change_rate > 0 ? '+' : '' }}{{ stockDetail.latest_change_rate | formatPercent }}
                            ({{ stockDetail.latest_change_amount > 0 ? '+' : '' }}{{ stockDetail.latest_change_amount | formatNumber(3) }})
                        </span>
                    </div>
                </div>

                <!-- 期间涨跌幅 -->
                <div class="period-changes">
                    <h4>期间涨跌幅</h4>
                    <el-row :gutter="20">
                        <el-col :span="4" v-for="(change, period) in periodChanges" :key="period">
                            <div class="change-item">
                                <div class="period-label">{{ getPeriodLabel(period) }}</div>
                                <div class="change-value" :class="getPriceColorClass(change.change_rate)">
                                    {{ change.change_rate === '--' ? '--' : (change.change_rate > 0 ? '+' : '') + formatPercent(change.change_rate) }}
                                </div>
                            </div>
                        </el-col>
                    </el-row>
                </div>

                <!-- 图表选择 -->
                <div class="chart-controls">
                    <h4>走势图</h4>
                    <el-radio-group v-model="selectedChartPeriod" @change="loadChartData">
                        <el-radio-button label="1min">1分钟</el-radio-button>
                        <el-radio-button label="1D">1日</el-radio-button>
                        <el-radio-button label="1W">1周</el-radio-button>
                        <el-radio-button label="1M">1月</el-radio-button>
                        <el-radio-button label="3M">3月</el-radio-button>
                        <el-radio-button label="6M">6月</el-radio-button>
                    </el-radio-group>
                </div>

                <!-- 股票走势图 -->
                <div class="chart-container" ref="comparisonChart"></div>

                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <el-button type="primary" @click="addToFavorites">
                        {{ stockDetail.is_favorited ? '已收藏' : '收藏' }}
                    </el-button>
                    <el-button type="success" @click="quickBuy">快速买入</el-button>
                </div>
            </div>
        </div>
    `,
    
    data() {
        return {
            loading: false,
            stockDetail: null,
            periodChanges: {},
            selectedChartPeriod: '1D',
            comparisonChart: null
        };
    },
    
    watch: {
        stockCode: {
            immediate: true,
            handler(newCode, oldCode) {
                if (newCode && newCode !== oldCode) {
                    // 清理旧的图表
                    this.clearCharts();
                    // 重置数据
                    this.stockDetail = null;
                    this.periodChanges = {};
                    // 加载新的股票详情
                    this.loadStockDetail();
                }
            }
        }
    },
    
    methods: {
        // 加载股票详情
        async loadStockDetail() {
            this.loading = true;
            
            // 并行加载股票详情和期间涨跌幅
            const [detailResult, changesResult] = await Promise.all([
                API.stocks.getDetail(this.stockCode),
                API.stocks.getPeriodChanges(this.stockCode)
            ]);
            
            this.loading = false;
            
            if (detailResult.success) {
                this.stockDetail = detailResult.data;
            }
            
            if (changesResult.success) {
                this.periodChanges = changesResult.data;
            }
            
            // 加载图表数据
            this.$nextTick(() => {
                this.loadChartData();
            });
        },
        
        // 加载图表数据
        async loadChartData() {
            if (!this.$refs.comparisonChart) return;

            // 只加载股票数据
            const stockResult = await API.stocks.getChartData(this.stockCode, this.selectedChartPeriod);

            if (stockResult.success) {
                if (stockResult.data && stockResult.data.length > 0) {
                    this.renderStockChart(stockResult.data);
                } else {
                    this.renderNoDataChart();
                }
            } else {
                this.renderNoDataChart();
            }
        },
        
        // 渲染股票图表
        renderStockChart(stockData) {
            if (!this.comparisonChart) {
                this.comparisonChart = echarts.init(this.$refs.comparisonChart);
            }

            // 处理时间轴数据
            const timeData = stockData.map(item => {
                const date = new Date(item.datetime);
                if (this.selectedChartPeriod === '1D' || this.selectedChartPeriod === '1min') {
                    return date.toLocaleTimeString().slice(0, 5); // 只显示时间
                } else {
                    return date.toLocaleDateString(); // 显示日期
                }
            });

            // 计算归一化数据（以第一个数据点为基准）
            const normalizeData = (data) => {
                if (data.length === 0) return [];
                const baseValue = data[0].close_price;
                return data.map(item => ((item.close_price - baseValue) / baseValue * 100).toFixed(2));
            };

            const stockNormalized = normalizeData(stockData);

            const option = {
                title: {
                    text: `${this.stockDetail.stock_name} 走势图`,
                    left: 'center'
                },
                tooltip: {
                    trigger: 'axis',
                    formatter: function(params) {
                        let result = params[0].name + '<br/>';
                        params.forEach(param => {
                            const color = param.color;
                            const seriesName = param.seriesName;
                            const value = param.value;
                            result += `<span style="color:${color}">●</span> ${seriesName}: ${value}%<br/>`;
                        });
                        return result;
                    }
                },
                legend: {
                    data: [this.stockDetail.stock_name],
                    top: 30
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: timeData,
                    axisLabel: {
                        rotate: 45
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}%'
                    }
                },
                series: [
                    {
                        name: this.stockDetail.stock_name,
                        type: 'line',
                        data: stockNormalized,
                        smooth: true,
                        lineStyle: {
                            color: '#E74C3C',
                            width: 2
                        },
                        itemStyle: {
                            color: '#E74C3C'
                        }
                    }
                ]
            };

            this.comparisonChart.setOption(option);
        },

        // 渲染无数据图表
        renderNoDataChart() {
            if (!this.comparisonChart) {
                this.comparisonChart = echarts.init(this.$refs.comparisonChart);
            }

            const option = {
                title: {
                    text: `${this.stockDetail.stock_name} 走势图`,
                    left: 'center'
                },
                graphic: {
                    type: 'text',
                    left: 'center',
                    top: 'middle',
                    style: {
                        text: `暂无${this.getPeriodLabel(this.selectedChartPeriod)}数据\n\n该股票可能是新上市股票或停牌股票\n请尝试选择其他时间周期或查看其他股票`,
                        fontSize: 16,
                        fontWeight: 'normal',
                        fill: '#999',
                        textAlign: 'center'
                    }
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    show: false
                },
                yAxis: {
                    type: 'value',
                    show: false
                },
                series: []
            };

            this.comparisonChart.setOption(option);
        },
        
        // 获取期间标签
        getPeriodLabel(period) {
            const labels = {
                '1min': '1分钟',
                '1D': '1日',
                '1W': '1周',
                '1M': '1月',
                '3M': '3月',
                '6M': '6月'
            };
            return labels[period] || period;
        },
        
        // 格式化百分比
        formatPercent(value) {
            return Utils.formatPercent(value);
        },
        
        // 获取价格颜色类
        getPriceColorClass(value) {
            return Utils.getPriceColorClass(value);
        },
        
        // 添加到收藏
        async addToFavorites() {
            const result = await API.favorites.add(this.stockCode);
            if (result.success) {
                this.$message.success('添加收藏成功');
                this.stockDetail.is_favorited = true;
            } else {
                if (result.error.includes('身份认证') || result.error.includes('403')) {
                    this.$message.error('请先登录后再收藏股票');
                } else {
                    this.$message.error('添加收藏失败: ' + result.error);
                }
            }
        },
        
        // 快速买入
        quickBuy() {
            this.$prompt('请输入买入数量', '快速买入', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                inputPattern: /^\d+$/,
                inputErrorMessage: '请输入有效的数量'
            }).then(async ({ value }) => {
                const result = await API.trading.quickBuy(this.stockCode, parseInt(value));
                if (result.success) {
                    this.$message.success('买入委托创建成功');
                } else {
                    if (result.error.includes('身份认证') || result.error.includes('403')) {
                        this.$message.error('请先登录后再进行交易');
                    } else {
                        this.$message.error('买入失败: ' + result.error);
                    }
                }
            });
        },

        // 清理图表
        clearCharts() {
            if (this.comparisonChart) {
                this.comparisonChart.dispose();
                this.comparisonChart = null;
            }
        }
    },
    
    beforeDestroy() {
        this.clearCharts();
    }
});
