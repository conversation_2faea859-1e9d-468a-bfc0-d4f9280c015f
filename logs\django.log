Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 8252
"GET /static/js/components/home.js HTTP/1.1" 200 16913
"GET /static/js/components/admin.js HTTP/1.1" 200 11209
"GET /static/js/utils/api.js HTTP/1.1" 200 8974
"GET /static/js/main.js HTTP/1.1" 200 8085
"GET /static/js/components/favorites.js HTTP/1.1" 200 4643
"GET /static/js/components/strategies.js HTTP/1.1" 200 18478
"GET /static/js/components/profile.js HTTP/1.1" 200 12620
"GET /static/js/components/trading.js HTTP/1.1" 200 17769
"GET /static/css/main.css HTTP/1.1" 200 5068
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 8252
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2938
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 8252
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 268
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 194630
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/accounts/profile/ HTTP/1.1" 200 234
"GET /api/accounts/profile/ HTTP/1.1" 200 234
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 194630
"GET /api/stocks/ranking/?period=1M&limit=20 HTTP/1.1" 200 3140
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 194612
"GET /api/stocks/ranking/?period=1M&limit=20 HTTP/1.1" 200 3140
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 194669
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 194681
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\services.py changed, reloading.
Watching for file changes with StatReloader
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\services.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/accounts/profile/ HTTP/1.1" 200 234
"GET /api/accounts/profile/ HTTP/1.1" 200 234
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 8252
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 234
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 186009
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 186009
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/accounts/profile/ HTTP/1.1" 200 234
"GET /api/accounts/profile/ HTTP/1.1" 200 234
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 186009
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 186009
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 186009
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 186009
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 186009
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 28, in get
    return Response(serializer.data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 768, in data
    ret = super().data
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 253, in data
    self._data = self.to_representation(self.instance)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 686, in to_representation
    return [
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 687, in <listcomp>
    self.child.to_representation(item) for item in iterable
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 522, in to_representation
    ret[field.field_name] = field.to_representation(attribute)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1081, in to_representation
    quantized = self.quantize(value)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\fields.py", line 1100, in quantize
    return value.quantize(
decimal.InvalidOperation: [<class 'decimal.InvalidOperation'>]
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 186009
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\serializers.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET / HTTP/1.1" 200 8252
"GET /api/accounts/profile/ HTTP/1.1" 200 234
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3221
"GET /api/stocks/ranking/?period=1Y&limit=5 HTTP/1.1" 200 803
"GET /api/stocks/000070/ HTTP/1.1" 200 183
"GET /api/stocks/000070/changes/ HTTP/1.1" 200 366
"GET /api/stocks/000070/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000070/chart/?period=1W HTTP/1.1" 200 1049
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/000070/chart/?period=1M HTTP/1.1" 200 3838
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/000070/chart/?period=3M HTTP/1.1" 200 10232
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/000070/chart/?period=1M HTTP/1.1" 200 3838
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/000070/chart/?period=3M HTTP/1.1" 200 10232
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/000070/chart/?period=6M HTTP/1.1" 200 19911
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/000070/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/000070/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000070/chart/?period=1W HTTP/1.1" 200 1049
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/accounts/profile/ HTTP/1.1" 200 234
"GET /api/accounts/profile/ HTTP/1.1" 200 234
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3221
"GET /api/stocks/ranking/?period=1M&limit=20 HTTP/1.1" 200 3199
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3221
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3221
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3221
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3221
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3221
"GET /api/stocks/ranking/?period=1M&limit=20 HTTP/1.1" 200 3199
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3221
"GET /api/stocks/ranking/?period=1M&limit=20 HTTP/1.1" 200 3199
"GET /api/stocks/000028/ HTTP/1.1" 200 182
"GET /api/stocks/000028/changes/ HTTP/1.1" 200 361
"GET /api/stocks/000028/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000028/chart/?period=1W HTTP/1.1" 200 1044
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/000028/chart/?period=1M HTTP/1.1" 200 3812
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/000028/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000028/chart/?period=1M HTTP/1.1" 200 3812
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/000028/chart/?period=3M HTTP/1.1" 200 10340
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/000028/chart/?period=6M HTTP/1.1" 200 20222
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\services.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 8252
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 234
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3221
"GET /api/stocks/000069/ HTTP/1.1" 200 184
"GET /api/stocks/000069/changes/ HTTP/1.1" 200 352
"GET /api/stocks/000069/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000069/chart/?period=1W HTTP/1.1" 200 1046
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/000069/chart/?period=3M HTTP/1.1" 200 10247
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/000069/chart/?period=6M HTTP/1.1" 200 19954
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET / HTTP/1.1" 200 8252
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
Internal Server Error: /api/accounts/login/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Python310\lib\site-packages\MySQLdb\cursors.py", line 206, in execute
    res = self._query(query)
  File "C:\Python310\lib\site-packages\MySQLdb\cursors.py", line 319, in _query
    db.query(q)
  File "C:\Python310\lib\site-packages\MySQLdb\connections.py", line 254, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1054, "Unknown column 'users.last_login' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\accounts\views.py", line 39, in post
    if serializer.is_valid():
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 227, in is_valid
    self._validated_data = self.run_validation(self.initial_data)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\serializers.py", line 429, in run_validation
    value = self.validate(value)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\accounts\serializers.py", line 36, in validate
    user = authenticate(username=username, password=password)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\debug.py", line 42, in sensitive_variables_wrapper
    return func(*func_args, **func_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\contrib\auth\__init__.py", line 77, in authenticate
    user = backend.authenticate(request, **credentials)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\contrib\auth\backends.py", line 46, in authenticate
    user = UserModel._default_manager.get_by_natural_key(username)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\contrib\auth\base_user.py", line 54, in get_by_natural_key
    return self.get(**{self.model.USERNAME_FIELD: username})
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\models\query.py", line 633, in get
    num = len(clone)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\models\query.py", line 380, in __len__
    self._fetch_all()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\models\sql\compiler.py", line 1560, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Python310\lib\site-packages\MySQLdb\cursors.py", line 206, in execute
    res = self._query(query)
  File "C:\Python310\lib\site-packages\MySQLdb\cursors.py", line 319, in _query
    db.query(q)
  File "C:\Python310\lib\site-packages\MySQLdb\connections.py", line 254, in query
    _mysql.connection.query(self, query)
django.db.utils.OperationalError: (1054, "Unknown column 'users.last_login' in 'field list'")
"POST /api/accounts/login/ HTTP/1.1" 500 231160
Bad Request: /api/accounts/register/
"POST /api/accounts/register/ HTTP/1.1" 400 59
Bad Request: /api/accounts/register/
"POST /api/accounts/register/ HTTP/1.1" 400 59
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 8252
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 285
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/ranking/?period=1M&limit=20 HTTP/1.1" 200 2402
"GET /api/stocks/ranking/?period=1M&limit=20 HTTP/1.1" 200 2402
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/000725/ HTTP/1.1" 200 185
"GET /api/stocks/000725/changes/ HTTP/1.1" 200 362
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=1W HTTP/1.1" 200 1021
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=1M HTTP/1.1" 200 3746
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=3M HTTP/1.1" 200 11071
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=6M HTTP/1.1" 200 21966
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/stocks/000725/ HTTP/1.1" 200 185
"GET /api/stocks/000725/changes/ HTTP/1.1" 200 362
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
Not Found: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 404 27
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET / HTTP/1.1" 200 8252
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/000725/ HTTP/1.1" 200 185
"GET /api/stocks/000725/changes/ HTTP/1.1" 200 362
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=1M HTTP/1.1" 200 3746
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET / HTTP/1.1" 200 8252
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 8252
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/000725/ HTTP/1.1" 200 185
"GET /api/stocks/000725/changes/ HTTP/1.1" 200 362
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=1W HTTP/1.1" 200 1021
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=1M HTTP/1.1" 200 3746
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=3M HTTP/1.1" 200 11071
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=6M HTTP/1.1" 200 21966
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\urls.py changed, reloading.
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/000725/ HTTP/1.1" 200 185
"GET /api/stocks/000725/changes/ HTTP/1.1" 200 362
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
Forbidden: /api/trading/orders/create/
"POST /api/trading/orders/create/ HTTP/1.1" 403 45
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 8252
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/000725/ HTTP/1.1" 200 185
"GET /api/stocks/000725/changes/ HTTP/1.1" 200 362
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=1W HTTP/1.1" 200 1021
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=1M HTTP/1.1" 200 3746
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=3M HTTP/1.1" 200 11071
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=6M HTTP/1.1" 200 21966
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=3M HTTP/1.1" 200 11071
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/000725/chart/?period=1M HTTP/1.1" 200 3746
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\urls.py changed, reloading.
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\urls.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2013
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2013
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 473
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=3M HTTP/1.1" 200 11071
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=6M HTTP/1.1" 200 21966
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=3M HTTP/1.1" 200 11071
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=1W HTTP/1.1" 200 1021
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 473
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/000725/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=1W HTTP/1.1" 200 1021
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 473
"GET /api/stocks/000725/chart/?period=1M HTTP/1.1" 200 3746
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=3M HTTP/1.1" 200 11071
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=6M HTTP/1.1" 200 21966
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/000725/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2013
"GET /api/stocks/600887/ HTTP/1.1" 200 192
"GET /api/stocks/600887/changes/ HTTP/1.1" 200 369
"GET /api/stocks/600887/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2013
"GET /api/stocks/600887/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/600887/chart/?period=1W HTTP/1.1" 200 1023
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 473
"GET /api/stocks/600887/chart/?period=1M HTTP/1.1" 200 3748
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2013
"GET /api/stocks/600887/chart/?period=3M HTTP/1.1" 200 11073
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2013
"GET /api/stocks/600887/chart/?period=6M HTTP/1.1" 200 21975
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2013
"GET /api/stocks/600276/ HTTP/1.1" 200 190
"GET /api/stocks/600276/changes/ HTTP/1.1" 200 365
"GET /api/stocks/600276/chart/?period=6M HTTP/1.1" 200 21980
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2013
"GET /api/stocks/000725/ HTTP/1.1" 200 185
"GET /api/stocks/000725/changes/ HTTP/1.1" 200 362
"GET /api/stocks/000725/chart/?period=6M HTTP/1.1" 200 21966
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2013
"GET /api/stocks/000001/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000001/chart/?period=1D HTTP/1.1" 200 40329
"GET /api/stocks/000001/chart/?period=1W HTTP/1.1" 200 1023
"GET /api/stocks/000001/chart/?period=1M HTTP/1.1" 200 3746
"GET / HTTP/1.1" 200 8252
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 200 17909
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/000725/ HTTP/1.1" 200 185
"GET /api/stocks/000725/changes/ HTTP/1.1" 200 362
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 473
"GET /api/stocks/000725/chart/?period=1W HTTP/1.1" 200 1021
"GET /api/stocks/000001/chart/?period=1D HTTP/1.1" 200 40329
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=1M HTTP/1.1" 200 3746
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=3M HTTP/1.1" 200 11071
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=6M HTTP/1.1" 200 21966
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000001/chart/?period=1W HTTP/1.1" 200 1023
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 473
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 473
"GET /api/stocks/000725/chart/?period=1W HTTP/1.1" 200 1021
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=6M HTTP/1.1" 200 21966
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/000725/ HTTP/1.1" 200 185
"GET /api/stocks/000725/changes/ HTTP/1.1" 200 362
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=3M HTTP/1.1" 200 11071
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=6M HTTP/1.1" 200 21966
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 473
"GET /api/stocks/000725/chart/?period=1W HTTP/1.1" 200 1021
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=1M HTTP/1.1" 200 3746
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 71
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 473
"GET /api/stocks/000725/chart/?period=1W HTTP/1.1" 200 1021
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=1M HTTP/1.1" 200 3746
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=3M HTTP/1.1" 200 11071
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2013
"GET /api/stocks/000725/chart/?period=6M HTTP/1.1" 200 21966
Forbidden: /api/trading/000725/quick-buy/
"POST /api/trading/000725/quick-buy/ HTTP/1.1" 403 45
"GET /api/stocks/ranking/?period=1M&limit=20 HTTP/1.1" 200 2402
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2404
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 39127
"GET /api/stocks/000725/chart/?period=1W HTTP/1.1" 200 993
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/000725/chart/?period=1D HTTP/1.1" 200 39127
"GET / HTTP/1.1" 200 8252
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 200 18704
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300059/chart/?period=1W HTTP/1.1" 200 1016
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=1M HTTP/1.1" 200 3731
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=3M HTTP/1.1" 200 10345
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=6M HTTP/1.1" 200 20345
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=1min HTTP/1.1" 200 19953
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300059/chart/?period=1W HTTP/1.1" 200 1016
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=1M HTTP/1.1" 200 3731
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=3M HTTP/1.1" 200 10345
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=6M HTTP/1.1" 200 20345
"GET /api/stocks/002594/ HTTP/1.1" 200 181
"GET /api/stocks/002594/changes/ HTTP/1.1" 200 366
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/002594/chart/?period=6M HTTP/1.1" 200 20688
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/002594/chart/?period=1D HTTP/1.1" 200 40853
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/002594/chart/?period=1W HTTP/1.1" 200 1036
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/002594/chart/?period=1M HTTP/1.1" 200 3794
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/002594/chart/?period=3M HTTP/1.1" 200 10518
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/002594/chart/?period=6M HTTP/1.1" 200 20688
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=6M HTTP/1.1" 200 20345
"GET /api/stocks/600030/ HTTP/1.1" 200 190
"GET /api/stocks/600030/changes/ HTTP/1.1" 200 352
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/600030/chart/?period=6M HTTP/1.1" 200 20262
"GET / HTTP/1.1" 200 8252
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=1min HTTP/1.1" 200 19953
"GET /api/stocks/600030/ HTTP/1.1" 200 190
"GET /api/stocks/600030/changes/ HTTP/1.1" 200 352
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/600030/chart/?period=1min HTTP/1.1" 200 19915
"GET / HTTP/1.1" 200 8252
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/600030/ HTTP/1.1" 200 190
"GET /api/stocks/600030/changes/ HTTP/1.1" 200 352
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600030/chart/?period=1D HTTP/1.1" 200 40020
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/600030/chart/?period=1min HTTP/1.1" 200 19915
"GET / HTTP/1.1" 200 8252
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/002594/ HTTP/1.1" 200 181
"GET /api/stocks/002594/changes/ HTTP/1.1" 200 366
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/002594/chart/?period=1D HTTP/1.1" 200 40853
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/002594/chart/?period=1min HTTP/1.1" 200 20324
"GET / HTTP/1.1" 200 8252
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/002594/ HTTP/1.1" 200 181
"GET /api/stocks/002594/changes/ HTTP/1.1" 200 366
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/002594/chart/?period=1D HTTP/1.1" 200 40853
Forbidden: /api/trading/002594/quick-buy/
"POST /api/trading/002594/quick-buy/ HTTP/1.1" 403 45
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET / HTTP/1.1" 200 8252
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
C:\Users\<USER>\PycharmProjects\pythonProject49\trading\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\trading\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\trading\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\trading\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\trading\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\strategies\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\strategies\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\strategies\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\strategies\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\accounts\views.py changed, reloading.
Watching for file changes with StatReloader
Forbidden: /api/trading/000725/quick-buy/
"POST /api/trading/000725/quick-buy/ HTTP/1.1" 403 43
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 43
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
Not Found: /test_csrf.html
"GET /test_csrf.html HTTP/1.1" 404 2947
"GET / HTTP/1.1" 200 8252
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/600030/ HTTP/1.1" 200 190
"GET /api/stocks/600030/changes/ HTTP/1.1" 200 352
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600030/chart/?period=1D HTTP/1.1" 200 40020
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
Watching for file changes with StatReloader
Bad Request: /api/accounts/login/
"POST /api/accounts/login/ HTTP/1.1" 400 49
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 43
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET /api/stocks/600030/ HTTP/1.1" 200 190
"GET /api/stocks/600030/changes/ HTTP/1.1" 200 352
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600030/chart/?period=1D HTTP/1.1" 200 40020
"GET / HTTP/1.1" 200 8252
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/600030/ HTTP/1.1" 200 190
"GET /api/stocks/600030/changes/ HTTP/1.1" 200 352
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600030/chart/?period=1D HTTP/1.1" 200 40020
Forbidden: /api/trading/600030/quick-buy/
"POST /api/trading/600030/quick-buy/ HTTP/1.1" 403 45
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET / HTTP/1.1" 200 8252
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET /api/stocks/600030/ HTTP/1.1" 200 190
"GET /api/stocks/600030/changes/ HTTP/1.1" 200 352
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600030/chart/?period=1D HTTP/1.1" 200 40020
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET /api/stocks/600000/ HTTP/1.1" 200 184
"GET /api/stocks/600000/changes/ HTTP/1.1" 200 357
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600000/chart/?period=1D HTTP/1.1" 200 39967
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET / HTTP/1.1" 200 8252
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 200 19723
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
Forbidden: /api/trading/300059/quick-buy/
"POST /api/trading/300059/quick-buy/ HTTP/1.1" 403 45
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
Forbidden: /api/accounts/logout/
"POST /api/accounts/logout/ HTTP/1.1" 403 45
Forbidden: /api/accounts/logout/
"POST /api/accounts/logout/ HTTP/1.1" 403 45
"GET / HTTP/1.1" 200 8252
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 200 8684
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
Forbidden: /api/accounts/logout/
"POST /api/accounts/logout/ HTTP/1.1" 403 45
"GET / HTTP/1.1" 200 8252
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
Forbidden: /api/accounts/logout/
"POST /api/accounts/logout/ HTTP/1.1" 403 45
Forbidden: /api/accounts/logout/
"POST /api/accounts/logout/ HTTP/1.1" 403 45
"GET / HTTP/1.1" 200 8252
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 200 8837
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
Forbidden: /api/accounts/logout/
"POST /api/accounts/logout/ HTTP/1.1" 403 45
Forbidden: /api/accounts/logout/
"POST /api/accounts/logout/ HTTP/1.1" 403 45
Forbidden: /api/accounts/logout/
"POST /api/accounts/logout/ HTTP/1.1" 403 45
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
Forbidden: /api/stocks/favorites/add/
"POST /api/stocks/favorites/add/ HTTP/1.1" 403 45
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET / HTTP/1.1" 200 8252
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET / HTTP/1.1" 200 8346
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
Forbidden: /api/accounts/logout/
"POST /api/accounts/logout/ HTTP/1.1" 403 45
"GET / HTTP/1.1" 200 8346
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
Forbidden: /api/accounts/logout/
"POST /api/accounts/logout/ HTTP/1.1" 403 45
"GET / HTTP/1.1" 200 8449
"GET /static/css/main.css HTTP/1.1" 200 5068
"GET /static/js/components/favorites.js HTTP/1.1" 200 4643
"GET /static/js/components/home.js HTTP/1.1" 200 19723
"GET /static/js/components/trading.js HTTP/1.1" 200 17769
"GET /static/js/components/profile.js HTTP/1.1" 200 13295
"GET /static/js/components/admin.js HTTP/1.1" 200 11209
"GET /static/js/components/strategies.js HTTP/1.1" 200 18478
"GET /static/js/main.js HTTP/1.1" 200 9601
"GET /static/js/utils/api.js HTTP/1.1" 200 9137
"GET /static/css/main.css HTTP/1.1" 200 5068
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 3049
"GET /api/accounts/profile/ HTTP/1.1" 200 251
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2938
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"POST /api/accounts/register/ HTTP/1.1" 201 60
"POST /api/accounts/login/ HTTP/1.1" 200 256
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"POST /api/stocks/favorites/add/ HTTP/1.1" 201 211
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/300059/ HTTP/1.1" 200 188
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"POST /api/trading/300059/quick-buy/ HTTP/1.1" 201 432
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 194
"GET /api/trading/profit-summary/ HTTP/1.1" 200 194
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/accounts/profile/ HTTP/1.1" 200 225
"GET /api/accounts/profile/ HTTP/1.1" 200 225
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 194
"GET /api/trading/profit-summary/ HTTP/1.1" 200 194
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/600276/ HTTP/1.1" 200 188
"GET /api/stocks/600276/changes/ HTTP/1.1" 200 354
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600276/chart/?period=1D HTTP/1.1" 200 40042
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 194
"GET /api/trading/profit-summary/ HTTP/1.1" 200 194
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 194
"GET /api/trading/profit-summary/ HTTP/1.1" 200 194
Bad Request: /api/trading/orders/create/
"POST /api/trading/orders/create/ HTTP/1.1" 400 24
Bad Request: /api/trading/orders/create/
"POST /api/trading/orders/create/ HTTP/1.1" 400 24
"POST /api/trading/orders/create/ HTTP/1.1" 201 428
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 198
"GET /api/trading/orders/history/ HTTP/1.1" 200 825
"GET /api/trading/positions/current/ HTTP/1.1" 200 767
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/history/ HTTP/1.1" 200 825
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/positions/current/ HTTP/1.1" 200 767
"GET /api/trading/records/ HTTP/1.1" 200 702
"GET /api/trading/orders/history/ HTTP/1.1" 200 825
"GET /api/trading/positions/current/ HTTP/1.1" 200 767
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 198
"GET /api/trading/profit-summary/ HTTP/1.1" 200 198
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"POST /api/strategies/grid/create/ HTTP/1.1" 201 722
"GET /api/strategies/running/ HTTP/1.1" 200 724
"GET /api/strategies/summary/ HTTP/1.1" 200 210
Bad Request: /api/strategies/martin/create/
"POST /api/strategies/martin/create/ HTTP/1.1" 400 68
"GET / HTTP/1.1" 200 8449
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 200 9405
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"GET / HTTP/1.1" 200 8449
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 200 9277
"GET /static/js/main.js HTTP/1.1" 304 0
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 199
"GET /api/trading/profit-summary/ HTTP/1.1" 200 199
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 199
"GET /api/trading/profit-summary/ HTTP/1.1" 200 199
"GET /api/trading/orders/history/ HTTP/1.1" 200 825
"GET /api/trading/positions/current/ HTTP/1.1" 200 767
"GET /api/trading/records/ HTTP/1.1" 200 702
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/strategies/running/ HTTP/1.1" 200 724
"GET /api/strategies/summary/ HTTP/1.1" 200 210
"GET /api/strategies/running/ HTTP/1.1" 200 724
"GET /api/strategies/summary/ HTTP/1.1" 200 210
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/strategies/summary/ HTTP/1.1" 200 210
"GET /api/strategies/running/ HTTP/1.1" 200 724
"GET /api/strategies/running/ HTTP/1.1" 200 724
"GET /api/strategies/summary/ HTTP/1.1" 200 210
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/300059/ HTTP/1.1" 200 188
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 199
"GET /api/trading/profit-summary/ HTTP/1.1" 200 199
"GET /api/strategies/running/ HTTP/1.1" 200 724
"GET /api/strategies/summary/ HTTP/1.1" 200 210
"GET /api/strategies/running/ HTTP/1.1" 200 724
"GET /api/strategies/summary/ HTTP/1.1" 200 210
"POST /api/strategies/martin/create/ HTTP/1.1" 201 735
"GET /api/strategies/running/ HTTP/1.1" 200 1404
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/300059/ HTTP/1.1" 200 188
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 351
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40120
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300059/chart/?period=1W HTTP/1.1" 200 1016
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=3M HTTP/1.1" 200 10345
"GET / HTTP/1.1" 200 8346
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 200 9129
"GET /static/js/main.js HTTP/1.1" 200 8823
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
Forbidden: /api/trading/profit-summary/
Forbidden: /api/trading/orders/current/
"GET /api/trading/profit-summary/ HTTP/1.1" 403 43
"GET /api/trading/orders/current/ HTTP/1.1" 403 43
Forbidden: /api/trading/profit-summary/
Forbidden: /api/trading/orders/current/
"GET /api/trading/profit-summary/ HTTP/1.1" 403 43
"GET /api/trading/orders/current/ HTTP/1.1" 403 43
Forbidden: /api/strategies/summary/
Forbidden: /api/strategies/running/
"GET /api/strategies/summary/ HTTP/1.1" 403 43
"GET /api/strategies/running/ HTTP/1.1" 403 43
Forbidden: /api/strategies/summary/
Forbidden: /api/strategies/running/
"GET /api/strategies/summary/ HTTP/1.1" 403 43
"GET /api/strategies/running/ HTTP/1.1" 403 43
Forbidden: /api/strategies/history/
"GET /api/strategies/history/ HTTP/1.1" 403 43
Forbidden: /api/strategies/running/
"GET /api/strategies/running/ HTTP/1.1" 403 43
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"GET / HTTP/1.1" 200 8346
"GET /static/css/main.css HTTP/1.1" 200 5434
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 200 26211
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 285
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/strategies/summary/ HTTP/1.1" 200 204
"GET /api/strategies/history/ HTTP/1.1" 200 52
"GET /api/strategies/running/ HTTP/1.1" 200 52
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"GET /api/accounts/profile/ HTTP/1.1" 200 251
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"GET / HTTP/1.1" 200 8346
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/2/ HTTP/1.1" 200 922
"GET /api/strategies/1/ HTTP/1.1" 200 1620
"GET / HTTP/1.1" 200 8346
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"GET / HTTP/1.1" 200 8346
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"GET / HTTP/1.1" 200 8346
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"GET / HTTP/1.1" 200 8346
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 200 9165
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"GET / HTTP/1.1" 200 8346
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"GET / HTTP/1.1" 200 8346
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"GET / HTTP/1.1" 200 8346
"GET /static/css/main.css HTTP/1.1" 200 5434
"GET /static/js/components/favorites.js HTTP/1.1" 200 4643
"GET /static/js/components/home.js HTTP/1.1" 200 19723
"GET /static/js/components/trading.js HTTP/1.1" 200 17769
"GET /static/js/components/admin.js HTTP/1.1" 200 11209
"GET /static/js/components/profile.js HTTP/1.1" 200 13295
"GET /static/js/components/strategies.js HTTP/1.1" 200 26211
"GET /static/js/main.js HTTP/1.1" 200 9010
"GET /static/js/utils/api.js HTTP/1.1" 200 9129
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2938
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1M&limit=20 HTTP/1.1" 200 2345
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET / HTTP/1.1" 200 8346
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET / HTTP/1.1" 200 8346
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"GET / HTTP/1.1" 200 8346
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"POST /api/accounts/logout/ HTTP/1.1" 200 26
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 3049
"GET / HTTP/1.1" 200 8472
"GET /static/js/main.js HTTP/1.1" 200 8548
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
Not Found: /.well-known/appspecific/com.chrome.devtools.json
"GET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1" 404 3049
"GET / HTTP/1.1" 200 8472
"GET /static/js/main.js HTTP/1.1" 200 8273
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"GET / HTTP/1.1" 200 8472
"GET /static/js/main.js HTTP/1.1" 304 0
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
Forbidden: /api/trading/profit-summary/
Forbidden: /api/trading/orders/current/
"GET /api/trading/profit-summary/ HTTP/1.1" 403 43
"GET /api/trading/orders/current/ HTTP/1.1" 403 43
Forbidden: /api/trading/profit-summary/
Forbidden: /api/trading/orders/current/
"GET /api/trading/profit-summary/ HTTP/1.1" 403 43
"GET /api/trading/orders/current/ HTTP/1.1" 403 43
Forbidden: /api/strategies/summary/
Forbidden: /api/strategies/running/
"GET /api/strategies/summary/ HTTP/1.1" 403 43
"GET /api/strategies/running/ HTTP/1.1" 403 43
Forbidden: /api/strategies/running/
Forbidden: /api/strategies/summary/
"GET /api/strategies/running/ HTTP/1.1" 403 43
"GET /api/strategies/summary/ HTTP/1.1" 403 43
Forbidden: /api/strategies/summary/
Forbidden: /api/strategies/running/
"GET /api/strategies/summary/ HTTP/1.1" 403 43
"GET /api/strategies/running/ HTTP/1.1" 403 43
Forbidden: /api/strategies/history/
"GET /api/strategies/history/ HTTP/1.1" 403 43
Forbidden: /api/strategies/running/
"GET /api/strategies/running/ HTTP/1.1" 403 43
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
Forbidden: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 403 43
Forbidden: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 403 43
Forbidden: /api/trading/profit-summary/
Forbidden: /api/trading/orders/current/
"GET /api/trading/profit-summary/ HTTP/1.1" 403 43
"GET /api/trading/orders/current/ HTTP/1.1" 403 43
Forbidden: /api/trading/profit-summary/
Forbidden: /api/trading/orders/current/
"GET /api/trading/profit-summary/ HTTP/1.1" 403 43
"GET /api/trading/orders/current/ HTTP/1.1" 403 43
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
Forbidden: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 403 43
Forbidden: /api/stocks/favorites/
"GET /api/stocks/favorites/ HTTP/1.1" 403 43
Forbidden: /api/trading/profit-summary/
Forbidden: /api/trading/orders/current/
"GET /api/trading/profit-summary/ HTTP/1.1" 403 43
"GET /api/trading/orders/current/ HTTP/1.1" 403 43
Forbidden: /api/trading/profit-summary/
Forbidden: /api/trading/orders/current/
"GET /api/trading/profit-summary/ HTTP/1.1" 403 43
"GET /api/trading/orders/current/ HTTP/1.1" 403 43
Forbidden: /api/strategies/summary/
Forbidden: /api/strategies/running/
"GET /api/strategies/summary/ HTTP/1.1" 403 43
"GET /api/strategies/running/ HTTP/1.1" 403 43
Forbidden: /api/strategies/summary/
Forbidden: /api/strategies/running/
"GET /api/strategies/summary/ HTTP/1.1" 403 43
"GET /api/strategies/running/ HTTP/1.1" 403 43
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"GET / HTTP/1.1" 200 8985
"GET /static/js/main.js HTTP/1.1" 200 9480
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET / HTTP/1.1" 200 8985
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET / HTTP/1.1" 200 8985
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET / HTTP/1.1" 200 8985
"GET /static/js/main.js HTTP/1.1" 200 10085
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET / HTTP/1.1" 200 8985
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 199
"GET /api/trading/profit-summary/ HTTP/1.1" 200 199
"GET /api/trading/orders/history/ HTTP/1.1" 200 1206
"GET /api/trading/positions/current/ HTTP/1.1" 200 767
"GET /api/trading/records/ HTTP/1.1" 200 1019
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"GET / HTTP/1.1" 200 8985
"GET /static/js/main.js HTTP/1.1" 304 0
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET / HTTP/1.1" 200 10125
"GET /static/js/main.js HTTP/1.1" 200 12371
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET / HTTP/1.1" 200 10125
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET / HTTP/1.1" 200 10125
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/css/main.css HTTP/1.1" 200 5711
"GET /static/js/utils/api.js HTTP/1.1" 200 9565
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET / HTTP/1.1" 200 10125
"GET /static/css/main.css HTTP/1.1" 200 5711
"GET /static/js/components/favorites.js HTTP/1.1" 200 4643
"GET /static/js/components/trading.js HTTP/1.1" 200 17769
"GET /static/js/components/home.js HTTP/1.1" 200 19723
"GET /static/js/components/strategies.js HTTP/1.1" 200 26211
"GET /static/js/components/admin.js HTTP/1.1" 200 11209
"GET /static/js/components/profile.js HTTP/1.1" 200 9718
"GET /static/js/utils/api.js HTTP/1.1" 200 9565
"GET /static/js/main.js HTTP/1.1" 200 12371
"GET /api/accounts/profile/ HTTP/1.1" 200 228
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2938
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2330
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"GET / HTTP/1.1" 200 10125
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
"GET / HTTP/1.1" 200 13666
"GET /static/css/main.css HTTP/1.1" 200 8804
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13666
"GET /static/js/components/favorites.js HTTP/1.1" 200 4643
"GET /static/js/components/admin.js HTTP/1.1" 200 11209
"GET /static/js/utils/api.js HTTP/1.1" 200 9565
"GET /static/js/main.js HTTP/1.1" 200 12371
"GET /static/js/components/home.js HTTP/1.1" 200 19723
"GET /static/css/main.css HTTP/1.1" 200 8804
"GET /static/js/components/strategies.js HTTP/1.1" 200 26211
"GET /static/js/components/trading.js HTTP/1.1" 200 17769
"GET /static/js/components/profile.js HTTP/1.1" 200 9718
"GET / HTTP/1.1" 200 13666
"GET / HTTP/1.1" 200 13666
"GET / HTTP/1.1" 200 13666
"GET / HTTP/1.1" 200 13666
"GET /static/css/main.css HTTP/1.1" 200 8804
"GET /static/js/components/home.js HTTP/1.1" 200 19723
"GET /static/js/components/favorites.js HTTP/1.1" 200 4643
"GET /static/js/components/trading.js HTTP/1.1" 200 17769
"GET /static/js/components/profile.js HTTP/1.1" 200 9718
"GET /static/js/components/strategies.js HTTP/1.1" 200 26211
"GET /static/js/components/admin.js HTTP/1.1" 200 11209
"GET /static/js/utils/api.js HTTP/1.1" 200 9565
"GET /static/js/main.js HTTP/1.1" 200 12371
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2938
"POST /api/accounts/login/ HTTP/1.1" 200 285
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2353
"GET /api/stocks/300059/ HTTP/1.1" 200 189
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 338
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300059/chart/?period=1W HTTP/1.1" 200 678
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=1M HTTP/1.1" 200 3562
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300059/chart/?period=1W HTTP/1.1" 200 678
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=3M HTTP/1.1" 200 10176
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=6M HTTP/1.1" 200 20345
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/stocks/favorites/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
"GET /api/trading/profit-summary/ HTTP/1.1" 200 189
Forbidden: /api/accounts/profile/
"GET /api/accounts/profile/ HTTP/1.1" 403 43
Not Found: /favicon.ico
"GET /favicon.ico HTTP/1.1" 404 2938
"POST /api/accounts/logout/ HTTP/1.1" 200 26
"POST /api/accounts/login/ HTTP/1.1" 200 262
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2353
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2353
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2353
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 199
"GET /api/trading/profit-summary/ HTTP/1.1" 200 199
"GET /api/trading/orders/history/ HTTP/1.1" 200 1206
"GET /api/trading/positions/current/ HTTP/1.1" 200 767
"GET /api/trading/records/ HTTP/1.1" 200 1019
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/stocks/favorites/ HTTP/1.1" 200 263
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2353
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2353
"GET /api/stocks/300059/ HTTP/1.1" 200 188
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 338
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/600000/ HTTP/1.1" 200 184
"GET /api/stocks/600000/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600000/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/600000/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/600000/chart/?period=1M HTTP/1.1" 200 3543
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/600000/chart/?period=3M HTTP/1.1" 200 10111
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/600000/chart/?period=6M HTTP/1.1" 200 20212
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/600000/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600000/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/600000/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/600000/chart/?period=1M HTTP/1.1" 200 3543
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/600000/chart/?period=3M HTTP/1.1" 200 10111
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/600000/chart/?period=6M HTTP/1.1" 200 20212
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/600000/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600000/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/600000/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600000/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/600000/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300059/ HTTP/1.1" 200 188
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 338
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=1min HTTP/1.1" 200 2
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 2336
"GET /api/stocks/300059/ HTTP/1.1" 200 188
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 357
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40170
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300059/chart/?period=1W HTTP/1.1" 200 1018
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=1M HTTP/1.1" 200 3902
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=3M HTTP/1.1" 200 10516
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300059/chart/?period=6M HTTP/1.1" 200 20685
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/2/ HTTP/1.1" 200 922
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/300059/ HTTP/1.1" 200 188
"GET /api/stocks/300059/changes/ HTTP/1.1" 200 357
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300059/chart/?period=1D HTTP/1.1" 200 40170
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
- Broken pipe from ('127.0.0.1', 55186)
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/history/ HTTP/1.1" 200 52
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/history/ HTTP/1.1" 200 52
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
- Broken pipe from ('127.0.0.1', 55225)
"GET /api/strategies/history/ HTTP/1.1" 200 52
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
- Broken pipe from ('127.0.0.1', 55233)
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/trading/orders/history/ HTTP/1.1" 200 1206
"GET /api/trading/positions/current/ HTTP/1.1" 200 774
"GET /api/trading/records/ HTTP/1.1" 200 1019
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/history/ HTTP/1.1" 200 52
"GET /api/strategies/running/ HTTP/1.1" 200 1406
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 203
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/002289/ HTTP/1.1" 200 185
"GET /api/stocks/002289/changes/ HTTP/1.1" 200 343
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/002289/chart/?period=6M HTTP/1.1" 200 19335
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/002289/chart/?period=1M HTTP/1.1" 200 3517
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/002289/chart/?period=3M HTTP/1.1" 200 9700
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/002289/chart/?period=1W HTTP/1.1" 200 672
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/002289/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/002289/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688382/ HTTP/1.1" 200 190
"GET /api/stocks/688382/changes/ HTTP/1.1" 200 338
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/688382/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688382/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688382/chart/?period=1W HTTP/1.1" 200 840
"GET /api/stocks/688382/chart/?period=1M HTTP/1.1" 200 3704
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688382/chart/?period=3M HTTP/1.1" 200 10432
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688382/chart/?period=6M HTTP/1.1" 200 20517
"GET /api/stocks/300718/ HTTP/1.1" 200 190
"GET /api/stocks/300718/changes/ HTTP/1.1" 200 340
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=6M HTTP/1.1" 200 20583
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300718/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300718/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=1M HTTP/1.1" 200 3704
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=3M HTTP/1.1" 200 10424
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=6M HTTP/1.1" 200 20583
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300718/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300718/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=1M HTTP/1.1" 200 3704
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=3M HTTP/1.1" 200 10424
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=6M HTTP/1.1" 200 20583
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300718/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300718/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=1M HTTP/1.1" 200 3704
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=3M HTTP/1.1" 200 10424
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=6M HTTP/1.1" 200 20583
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300718/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300718/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=1M HTTP/1.1" 200 3704
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=3M HTTP/1.1" 200 10424
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=6M HTTP/1.1" 200 20583
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=3M HTTP/1.1" 200 10424
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=1M HTTP/1.1" 200 3704
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=3M HTTP/1.1" 200 10424
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=6M HTTP/1.1" 200 20583
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300718/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/300718/chart/?period=6M HTTP/1.1" 200 20583
"GET /api/stocks/000506/ HTTP/1.1" 200 179
"GET /api/stocks/000506/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/000506/chart/?period=6M HTTP/1.1" 200 19798
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/000506/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/000506/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/000506/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/000506/chart/?period=1M HTTP/1.1" 200 3521
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/000506/chart/?period=3M HTTP/1.1" 200 9938
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/000506/chart/?period=6M HTTP/1.1" 200 19798
"GET /api/stocks/ranking/?period=1M&limit=20 HTTP/1.1" 200 3134
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\services.py changed, reloading.
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
Watching for file changes with StatReloader
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300204/ HTTP/1.1" 200 187
"GET /api/stocks/300204/changes/ HTTP/1.1" 200 338
"GET /api/stocks/300204/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/300204/chart/?period=6M HTTP/1.1" 200 20309
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 470
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2010
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET / HTTP/1.1" 200 13666
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2010
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3092
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 341
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1475
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4151
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1475
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 2
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13666
"GET /static/js/components/favorites.js HTTP/1.1" 304 0
"GET /static/js/utils/api.js HTTP/1.1" 304 0
"GET /static/js/main.js HTTP/1.1" 304 0
"GET /static/css/main.css HTTP/1.1" 304 0
"GET /static/js/components/admin.js HTTP/1.1" 304 0
"GET /static/js/components/profile.js HTTP/1.1" 304 0
"GET /static/js/components/strategies.js HTTP/1.1" 304 0
"GET /static/js/components/trading.js HTTP/1.1" 304 0
"GET /static/js/components/home.js HTTP/1.1" 200 19723
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1475
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4151
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 19081
"GET /api/stocks/300548/ HTTP/1.1" 200 191
"GET /api/stocks/300548/changes/ HTTP/1.1" 200 325
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/300548/chart/?period=1min HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300548/chart/?period=1D HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/300548/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1475
"GET /api/stocks/300548/chart/?period=1M HTTP/1.1" 200 3707
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4151
"GET /api/stocks/300548/chart/?period=3M HTTP/1.1" 200 10446
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/300548/chart/?period=6M HTTP/1.1" 200 20558
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/300548/chart/?period=1min HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300548/chart/?period=1D HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/300548/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300548/chart/?period=1D HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/300548/chart/?period=1min HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300548/chart/?period=1D HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/300548/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/300468/ HTTP/1.1" 200 190
"GET /api/stocks/300468/changes/ HTTP/1.1" 200 339
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/300468/chart/?period=1W HTTP/1.1" 200 845
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/300468/chart/?period=1min HTTP/1.1" 200 19584
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300468/chart/?period=1D HTTP/1.1" 200 19584
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/300468/chart/?period=1W HTTP/1.1" 200 845
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1475
"GET /api/stocks/300468/chart/?period=1M HTTP/1.1" 200 3715
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4151
"GET /api/stocks/300468/chart/?period=3M HTTP/1.1" 200 10477
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/300468/chart/?period=6M HTTP/1.1" 200 20593
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/300468/chart/?period=1min HTTP/1.1" 200 19584
"GET /api/stocks/300548/ HTTP/1.1" 200 191
"GET /api/stocks/300548/changes/ HTTP/1.1" 200 325
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/300548/chart/?period=1min HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300548/chart/?period=1D HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/300548/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1475
"GET /api/stocks/300548/chart/?period=1M HTTP/1.1" 200 3707
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4151
"GET /api/stocks/300548/chart/?period=3M HTTP/1.1" 200 10446
"GET /api/stocks/300548/chart/?period=6M HTTP/1.1" 200 20558
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"GET /api/trading/orders/history/ HTTP/1.1" 200 1206
"GET /api/strategies/running/ HTTP/1.1" 200 1408
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1408
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/300548/ HTTP/1.1" 200 191
"GET /api/stocks/300548/changes/ HTTP/1.1" 200 325
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300548/chart/?period=1D HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/300548/chart/?period=1min HTTP/1.1" 200 19505
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/300548/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1475
"GET /api/stocks/300548/chart/?period=1M HTTP/1.1" 200 3707
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4151
"GET /api/stocks/300548/chart/?period=3M HTTP/1.1" 200 10446
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/300548/chart/?period=6M HTTP/1.1" 200 20558
"GET /api/stocks/300476/ HTTP/1.1" 200 187
"GET /api/stocks/300476/changes/ HTTP/1.1" 200 340
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/300476/chart/?period=6M HTTP/1.1" 200 20718
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300476/chart/?period=1D HTTP/1.1" 200 20037
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/300476/chart/?period=1W HTTP/1.1" 200 862
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1475
"GET /api/stocks/300476/chart/?period=1M HTTP/1.1" 200 3797
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4151
"GET /api/stocks/300476/chart/?period=3M HTTP/1.1" 200 10604
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/300476/chart/?period=6M HTTP/1.1" 200 20718
"GET /api/stocks/300548/ HTTP/1.1" 200 191
"GET /api/stocks/300548/changes/ HTTP/1.1" 200 325
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/300548/chart/?period=6M HTTP/1.1" 200 20558
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300548/chart/?period=1D HTTP/1.1" 200 19505
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/300204/ HTTP/1.1" 200 187
"GET /api/stocks/300204/changes/ HTTP/1.1" 200 336
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300204/chart/?period=1D HTTP/1.1" 200 19543
"GET /api/stocks/688068/ HTTP/1.1" 200 189
"GET /api/stocks/688068/changes/ HTTP/1.1" 200 338
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688068/chart/?period=1D HTTP/1.1" 200 19912
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/300204/ HTTP/1.1" 200 187
"GET /api/stocks/300204/changes/ HTTP/1.1" 200 336
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300204/chart/?period=1D HTTP/1.1" 200 19543
"GET /api/stocks/300204/chart/?period=1min HTTP/1.1" 200 19543
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/300153/ HTTP/1.1" 200 189
"GET /api/stocks/300153/changes/ HTTP/1.1" 200 337
"GET /api/stocks/300153/chart/?period=6M HTTP/1.1" 200 20567
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1D HTTP/1.1" 200 19530
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1W HTTP/1.1" 200 843
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1M HTTP/1.1" 200 3708
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=3M HTTP/1.1" 200 10449
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=6M HTTP/1.1" 200 20567
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1min HTTP/1.1" 200 19530
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1D HTTP/1.1" 200 19530
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1min HTTP/1.1" 200 19530
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1D HTTP/1.1" 200 19530
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1W HTTP/1.1" 200 843
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1D HTTP/1.1" 200 19530
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1min HTTP/1.1" 200 19530
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1D HTTP/1.1" 200 19530
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1W HTTP/1.1" 200 843
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1min HTTP/1.1" 200 19530
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1W HTTP/1.1" 200 843
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=1M HTTP/1.1" 200 3708
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=3M HTTP/1.1" 200 10449
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=6M HTTP/1.1" 200 20567
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/600289/ HTTP/1.1" 200 186
"GET /api/stocks/600289/changes/ HTTP/1.1" 200 338
"GET /api/stocks/600289/chart/?period=6M HTTP/1.1" 200 19341
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/600289/chart/?period=1min HTTP/1.1" 200 18917
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/600289/chart/?period=1D HTTP/1.1" 200 18917
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/600289/chart/?period=1W HTTP/1.1" 200 820
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/600289/chart/?period=1M HTTP/1.1" 200 3443
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/600289/chart/?period=1min HTTP/1.1" 200 18917
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 17809
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/000506/ HTTP/1.1" 200 184
"GET /api/stocks/000506/changes/ HTTP/1.1" 200 335
"GET /api/stocks/000506/chart/?period=6M HTTP/1.1" 200 19966
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/000506/chart/?period=1W HTTP/1.1" 200 843
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/000506/chart/?period=1D HTTP/1.1" 200 19513
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000506/chart/?period=1W HTTP/1.1" 200 843
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/000506/chart/?period=1M HTTP/1.1" 200 3689
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/000506/chart/?period=1min HTTP/1.1" 200 18227
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/000506/chart/?period=1D HTTP/1.1" 200 19513
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/000506/chart/?period=1min HTTP/1.1" 200 18227
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/000506/chart/?period=1D HTTP/1.1" 200 19513
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688382/ HTTP/1.1" 200 190
"GET /api/stocks/688382/changes/ HTTP/1.1" 200 336
"GET /api/stocks/688382/chart/?period=1D HTTP/1.1" 200 19501
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688382/chart/?period=1W HTTP/1.1" 200 840
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/688382/chart/?period=1D HTTP/1.1" 200 19501
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
Watching for file changes with StatReloader
Not Found: /stocks/688382/
"GET /stocks/688382/ HTTP/1.1" 404 2947
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 13834
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1475
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4151
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8168
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4151
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1475
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 336
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 13675
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/300204/ HTTP/1.1" 200 187
"GET /api/stocks/300204/changes/ HTTP/1.1" 200 336
"GET /api/stocks/300204/chart/?period=1min HTTP/1.1" 200 14035
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300204/chart/?period=1D HTTP/1.1" 200 19543
"GET /api/stocks/300548/ HTTP/1.1" 200 191
"GET /api/stocks/300548/changes/ HTTP/1.1" 200 325
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300548/chart/?period=1D HTTP/1.1" 200 19505
"GET /api/stocks/300548/chart/?period=1min HTTP/1.1" 200 13849
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/300476/ HTTP/1.1" 200 187
"GET /api/stocks/300476/changes/ HTTP/1.1" 200 340
"GET /api/stocks/300476/chart/?period=1min HTTP/1.1" 200 14226
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/300476/chart/?period=1D HTTP/1.1" 200 20037
"GET /api/stocks/600289/ HTTP/1.1" 200 186
"GET /api/stocks/600289/changes/ HTTP/1.1" 200 338
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600289/chart/?period=1D HTTP/1.1" 200 18917
"GET /api/stocks/600289/chart/?period=1min HTTP/1.1" 200 13279
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 68
"GET /api/stocks/600289/chart/?period=1D HTTP/1.1" 200 18917
"GET /api/stocks/600289/chart/?period=1min HTTP/1.1" 200 13279
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1475
"GET / HTTP/1.1" 200 13666
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 72
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 353
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 12880
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1545
"GET /api/stocks/688622/ HTTP/1.1" 200 189
"GET /api/stocks/688622/changes/ HTTP/1.1" 200 337
"GET /api/stocks/688622/chart/?period=1min HTTP/1.1" 200 13245
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1545
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 72
"GET /api/stocks/688622/chart/?period=1D HTTP/1.1" 200 19508
"GET /api/stocks/300468/ HTTP/1.1" 200 190
"GET /api/stocks/300468/changes/ HTTP/1.1" 200 339
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 72
"GET /api/stocks/300468/chart/?period=1D HTTP/1.1" 200 19584
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 141
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/603226/ HTTP/1.1" 200 189
"GET /api/stocks/603226/changes/ HTTP/1.1" 200 334
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 141
"GET /api/stocks/603226/chart/?period=1D HTTP/1.1" 200 19472
"GET /api/stocks/603226/chart/?period=1min HTTP/1.1" 200 13020
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1614
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 141
"GET /api/stocks/603226/chart/?period=1D HTTP/1.1" 200 19472
"GET /api/stocks/603226/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 422
"GET /api/stocks/603226/chart/?period=1M HTTP/1.1" 200 3691
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1614
"GET /api/stocks/603226/chart/?period=3M HTTP/1.1" 200 9652
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4425
"GET /api/stocks/603226/chart/?period=6M HTTP/1.1" 200 19473
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8643
"GET /api/stocks/688382/ HTTP/1.1" 200 190
"GET /api/stocks/688382/changes/ HTTP/1.1" 200 336
"GET /api/stocks/688382/chart/?period=6M HTTP/1.1" 200 20517
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8643
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 141
"GET /api/stocks/688382/chart/?period=1D HTTP/1.1" 200 19501
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/strategies/running/ HTTP/1.1" 200 1408
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1408
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
Internal Server Error: /api/stocks/ranking/
Traceback (most recent call last):
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\decorators\csrf.py", line 56, in wrapper_view
    return view_func(*args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 509, in dispatch
    response = self.handle_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 469, in handle_exception
    self.raise_uncaught_exception(exc)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 480, in raise_uncaught_exception
    raise exc
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\rest_framework\views.py", line 506, in dispatch
    response = handler(request, *args, **kwargs)
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\views.py", line 26, in get
    service = StockDataService()
  File "C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\services.py", line 20, in __init__
    self.pro = ts.pro_api()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\tushare\pro\data_pro.py", line 36, in pro_api
    token = upass.get_token()
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\tushare\util\upass.py", line 27, in get_token
    df = pd.read_csv(fp)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\pandas\io\parsers\readers.py", line 912, in read_csv
    return _read(filepath_or_buffer, kwds)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\pandas\io\parsers\readers.py", line 577, in _read
    parser = TextFileReader(filepath_or_buffer, **kwds)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\pandas\io\parsers\readers.py", line 1407, in __init__
    self._engine = self._make_engine(f, self.engine)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\pandas\io\parsers\readers.py", line 1679, in _make_engine
    return mapping[engine](f, **self.options)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python310\site-packages\pandas\io\parsers\c_parser_wrapper.py", line 93, in __init__
    self._reader = parsers.TextReader(src, **kwds)
  File "pandas\_libs\parsers.pyx", line 557, in pandas._libs.parsers.TextReader.__cinit__
pandas.errors.EmptyDataError: No columns to parse from file
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 500 153132
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 142
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1615
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 423
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 142
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/300153/ HTTP/1.1" 200 189
"GET /api/stocks/300153/changes/ HTTP/1.1" 200 337
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 142
"GET /api/stocks/300153/chart/?period=1D HTTP/1.1" 200 19530
"GET /api/stocks/300153/chart/?period=1min HTTP/1.1" 200 11287
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 1615
"GET /api/stocks/300153/chart/?period=1W HTTP/1.1" 200 843
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 423
"GET /api/stocks/300153/chart/?period=1M HTTP/1.1" 200 3708
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 1615
"GET /api/stocks/300153/chart/?period=3M HTTP/1.1" 200 10449
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 4426
"GET /api/stocks/300153/chart/?period=6M HTTP/1.1" 200 20567
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 8644
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/300204/ HTTP/1.1" 200 187
"GET /api/stocks/300204/changes/ HTTP/1.1" 200 336
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/300204/chart/?period=1D HTTP/1.1" 200 19543
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300204/chart/?period=1min HTTP/1.1" 200 8374
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/300204/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/hs300/chart/?period=1M HTTP/1.1" 200 2
"GET /api/stocks/300204/chart/?period=1M HTTP/1.1" 200 3709
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/300204/chart/?period=3M HTTP/1.1" 200 10452
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/300204/chart/?period=6M HTTP/1.1" 200 20309
"GET /api/stocks/688382/ HTTP/1.1" 200 190
"GET /api/stocks/688382/changes/ HTTP/1.1" 200 336
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/688382/chart/?period=6M HTTP/1.1" 200 20517
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688382/chart/?period=1min HTTP/1.1" 200 8367
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 8110
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=1W HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/hs300/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 8110
"GET /api/stocks/hs300/chart/?period=1D HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/688068/ HTTP/1.1" 200 189
"GET /api/stocks/688068/changes/ HTTP/1.1" 200 338
"GET /api/stocks/hs300/chart/?period=6M HTTP/1.1" 200 2
"GET /api/stocks/688068/chart/?period=6M HTTP/1.1" 200 20752
"GET /api/stocks/hs300/chart/?period=3M HTTP/1.1" 200 2
"GET /api/stocks/688068/chart/?period=3M HTTP/1.1" 200 10634
"GET /api/stocks/search/?keyword=000001 HTTP/1.1" 200 186
"GET /api/stocks/search/?keyword=000001 HTTP/1.1" 200 186
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/stocks/favorites/ HTTP/1.1" 200 279
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"POST /api/trading/orders/create/ HTTP/1.1" 201 428
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"GET /api/trading/orders/history/ HTTP/1.1" 200 1594
Bad Request: /api/trading/orders/create/
"POST /api/trading/orders/create/ HTTP/1.1" 400 40
"GET /api/trading/positions/current/ HTTP/1.1" 200 1134
"GET /api/trading/records/ HTTP/1.1" 200 1345
"GET /api/trading/orders/history/ HTTP/1.1" 200 1594
"GET /api/strategies/running/ HTTP/1.1" 200 1407
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/summary/ HTTP/1.1" 200 211
"GET /api/strategies/running/ HTTP/1.1" 200 1407
"GET /api/strategies/2/ HTTP/1.1" 200 923
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/orders/current/ HTTP/1.1" 200 52
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"GET /api/trading/profit-summary/ HTTP/1.1" 200 205
"GET /api/trading/records/ HTTP/1.1" 200 1345
"GET /api/trading/positions/current/ HTTP/1.1" 200 1134
"GET / HTTP/1.1" 200 13666
"GET /static/js/components/home.js HTTP/1.1" 200 19775
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/688585/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/688585/chart/?period=1W HTTP/1.1" 200 675
"GET /api/stocks/688585/chart/?period=1M HTTP/1.1" 200 2689
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET /api/stocks/300436/ HTTP/1.1" 200 187
"GET /api/stocks/300436/changes/ HTTP/1.1" 200 339
"GET /api/stocks/300436/chart/?period=6M HTTP/1.1" 200 20473
"GET /api/stocks/300436/chart/?period=1D HTTP/1.1" 200 20011
"GET /api/stocks/300436/chart/?period=1W HTTP/1.1" 200 845
"GET /api/stocks/300436/chart/?period=1M HTTP/1.1" 200 3709
"GET /api/stocks/300436/chart/?period=3M HTTP/1.1" 200 10422
"GET /api/stocks/300436/chart/?period=6M HTTP/1.1" 200 20473
"GET /api/stocks/300436/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688656/ HTTP/1.1" 200 187
"GET /api/stocks/688656/changes/ HTTP/1.1" 200 326
"GET /api/stocks/688656/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300548/ HTTP/1.1" 200 191
"GET /api/stocks/300548/changes/ HTTP/1.1" 200 325
"GET /api/stocks/300548/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300548/chart/?period=1D HTTP/1.1" 200 19505
"GET /api/stocks/300548/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/300468/ HTTP/1.1" 200 190
"GET /api/stocks/300468/changes/ HTTP/1.1" 200 339
"GET /api/stocks/300468/chart/?period=1W HTTP/1.1" 200 845
"GET /api/stocks/300548/ HTTP/1.1" 200 191
"GET /api/stocks/300548/changes/ HTTP/1.1" 200 325
"GET /api/stocks/300548/chart/?period=1W HTTP/1.1" 200 844
"GET /api/stocks/300548/chart/?period=1M HTTP/1.1" 200 3707
"GET /api/stocks/300548/chart/?period=3M HTTP/1.1" 200 10446
"GET /api/stocks/300548/chart/?period=6M HTTP/1.1" 200 20558
"GET /api/stocks/300548/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300548/chart/?period=1D HTTP/1.1" 200 19505
"GET /api/stocks/300548/chart/?period=6M HTTP/1.1" 200 20558
"GET /api/stocks/300468/ HTTP/1.1" 200 190
"GET /api/stocks/300468/changes/ HTTP/1.1" 200 339
"GET /api/stocks/300468/chart/?period=6M HTTP/1.1" 200 20593
"GET /api/stocks/300548/ HTTP/1.1" 200 191
"GET /api/stocks/300548/changes/ HTTP/1.1" 200 325
"GET /api/stocks/300548/chart/?period=6M HTTP/1.1" 200 20558
"GET /api/stocks/300548/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300723/ HTTP/1.1" 200 186
"GET /api/stocks/300723/changes/ HTTP/1.1" 200 336
"GET /api/stocks/300723/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300723/chart/?period=6M HTTP/1.1" 200 20522
"GET /api/stocks/300723/chart/?period=3M HTTP/1.1" 200 10435
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3090
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/688585/chart/?period=3M HTTP/1.1" 200 9229
"GET /api/stocks/300436/ HTTP/1.1" 200 187
"GET /api/stocks/300436/changes/ HTTP/1.1" 200 339
"GET /api/stocks/300436/chart/?period=3M HTTP/1.1" 200 10422
"GET /api/stocks/300436/chart/?period=1D HTTP/1.1" 200 20011
"GET /api/stocks/300436/chart/?period=1W HTTP/1.1" 200 845
"GET /api/stocks/300436/chart/?period=1M HTTP/1.1" 200 3709
"GET /api/stocks/300436/chart/?period=3M HTTP/1.1" 200 10422
"GET /api/stocks/300436/chart/?period=6M HTTP/1.1" 200 20473
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\services.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
"GET / HTTP/1.1" 200 13666
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3086
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/300436/ HTTP/1.1" 200 187
"GET /api/stocks/300436/changes/ HTTP/1.1" 200 339
"GET /api/stocks/300436/chart/?period=1D HTTP/1.1" 200 20011
"GET /api/stocks/300436/chart/?period=6M HTTP/1.1" 200 20473
"GET /api/stocks/300436/chart/?period=1D HTTP/1.1" 200 20011
"GET /api/stocks/300436/chart/?period=1W HTTP/1.1" 200 845
"GET /api/stocks/300436/chart/?period=6M HTTP/1.1" 200 20473
"GET /api/stocks/300436/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688068/ HTTP/1.1" 200 189
"GET /api/stocks/688068/changes/ HTTP/1.1" 200 338
"GET /api/stocks/688068/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300153/ HTTP/1.1" 200 189
"GET /api/stocks/300153/changes/ HTTP/1.1" 200 337
"GET /api/stocks/300153/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3086
"GET /api/stocks/002052/ HTTP/1.1" 200 190
"GET /api/stocks/002052/changes/ HTTP/1.1" 200 334
"GET /api/stocks/002052/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/002052/chart/?period=6M HTTP/1.1" 200 20022
"GET /api/stocks/300972/ HTTP/1.1" 200 186
"GET /api/stocks/300972/changes/ HTTP/1.1" 200 342
"GET /api/stocks/300972/chart/?period=6M HTTP/1.1" 200 20759
"GET /api/stocks/300972/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/000001/chart/?period=1D HTTP/1.1" 200 39027
"GET / HTTP/1.1" 200 13666
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3086
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/688313/ HTTP/1.1" 200 190
"GET /api/stocks/688313/changes/ HTTP/1.1" 200 338
"GET /api/stocks/688313/chart/?period=1D HTTP/1.1" 200 19567
"GET /api/stocks/688313/chart/?period=6M HTTP/1.1" 200 19038
"GET /api/stocks/688313/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/688313/chart/?period=1D HTTP/1.1" 200 19567
"GET /api/stocks/688313/chart/?period=6M HTTP/1.1" 200 19038
"GET /api/stocks/000001/chart/?period=1D HTTP/1.1" 200 39027
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3086
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/688585/chart/?period=6M HTTP/1.1" 200 19032
"GET / HTTP/1.1" 200 13666
"GET /static/js/components/home.js HTTP/1.1" 304 0
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3086
"GET / HTTP/1.1" 200 13666
"GET /api/accounts/profile/ HTTP/1.1" 200 228
"GET /api/stocks/ranking/?period=1Y&limit=20 HTTP/1.1" 200 3086
"GET /api/stocks/688585/ HTTP/1.1" 200 189
"GET /api/stocks/688585/changes/ HTTP/1.1" 200 339
"GET /api/stocks/688585/chart/?period=1D HTTP/1.1" 200 19081
"GET /api/stocks/300153/ HTTP/1.1" 200 189
"GET /api/stocks/300153/changes/ HTTP/1.1" 200 337
"GET /api/stocks/300153/chart/?period=1D HTTP/1.1" 200 19530
"GET /api/stocks/300153/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300153/chart/?period=6M HTTP/1.1" 200 20567
"GET /api/stocks/688313/ HTTP/1.1" 200 190
"GET /api/stocks/688313/changes/ HTTP/1.1" 200 338
"GET /api/stocks/688313/chart/?period=6M HTTP/1.1" 200 19038
"GET /api/stocks/688313/chart/?period=3M HTTP/1.1" 200 8935
"GET /api/stocks/688313/chart/?period=1M HTTP/1.1" 200 2532
"GET /api/stocks/688313/chart/?period=1W HTTP/1.1" 200 845
"GET /api/stocks/688313/chart/?period=1D HTTP/1.1" 200 19567
"GET /api/stocks/688313/chart/?period=1min HTTP/1.1" 200 2
"GET /api/stocks/300723/ HTTP/1.1" 200 186
"GET /api/stocks/300723/changes/ HTTP/1.1" 200 336
"GET /api/stocks/300723/chart/?period=1min HTTP/1.1" 200 2
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\services.py changed, reloading.
C:\Users\<USER>\PycharmProjects\pythonProject49\stocks\services.py changed, reloading.
Watching for file changes with StatReloader
Watching for file changes with StatReloader
