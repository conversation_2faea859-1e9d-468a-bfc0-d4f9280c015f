from rest_framework import serializers
from .models import Stock, StockDailyData, StockMinuteData, HS300Data, UserFavorite
from datetime import datetime, timedelta


class StockSerializer(serializers.ModelSerializer):
    """股票基础信息序列化器"""
    
    class Meta:
        model = Stock
        fields = '__all__'


class StockDailyDataSerializer(serializers.ModelSerializer):
    """股票日线数据序列化器"""
    
    class Meta:
        model = StockDailyData
        fields = '__all__'


class StockMinuteDataSerializer(serializers.ModelSerializer):
    """股票分钟数据序列化器"""
    
    class Meta:
        model = StockMinuteData
        fields = '__all__'


class HS300DataSerializer(serializers.ModelSerializer):
    """沪深300数据序列化器"""
    
    class Meta:
        model = HS300Data
        fields = '__all__'


class StockDetailSerializer(serializers.ModelSerializer):
    """股票详情序列化器"""
    latest_price = serializers.SerializerMethodField()
    latest_change_rate = serializers.SerializerMethodField()
    latest_change_amount = serializers.SerializerMethodField()
    is_favorited = serializers.SerializerMethodField()
    
    class Meta:
        model = Stock
        fields = ('stock_code', 'stock_name', 'market', 'industry', 
                 'latest_price', 'latest_change_rate', 'latest_change_amount', 'is_favorited')
    
    def get_latest_price(self, obj):
        latest_data = StockDailyData.objects.filter(
            stock_code=obj.stock_code
        ).order_by('-trade_date').first()
        return latest_data.close_price if latest_data else None
    
    def get_latest_change_rate(self, obj):
        latest_data = StockDailyData.objects.filter(
            stock_code=obj.stock_code
        ).order_by('-trade_date').first()
        return latest_data.change_rate if latest_data else None
    
    def get_latest_change_amount(self, obj):
        latest_data = StockDailyData.objects.filter(
            stock_code=obj.stock_code
        ).order_by('-trade_date').first()
        return latest_data.change_amount if latest_data else None
    
    def get_is_favorited(self, obj):
        request = self.context.get('request')
        if request and request.user.is_authenticated:
            return UserFavorite.objects.filter(
                user=request.user, 
                stock_code=obj.stock_code
            ).exists()
        return False


class StockRankingSerializer(serializers.Serializer):
    """股票排行序列化器"""
    stock_code = serializers.CharField()
    stock_name = serializers.CharField()
    latest_price = serializers.FloatField()
    change_rate = serializers.FloatField()
    change_amount = serializers.FloatField()
    volume = serializers.IntegerField()
    market = serializers.CharField()


class UserFavoriteSerializer(serializers.ModelSerializer):
    """用户收藏序列化器"""
    stock_name = serializers.SerializerMethodField()
    current_price = serializers.SerializerMethodField()
    profit_since_favorite = serializers.SerializerMethodField()
    
    class Meta:
        model = UserFavorite
        fields = ('id', 'stock_code', 'stock_name', 'favorite_date', 
                 'favorite_price', 'current_price', 'profit_since_favorite', 'created_at')
        read_only_fields = ('id', 'created_at')
    
    def get_stock_name(self, obj):
        try:
            stock = Stock.objects.get(stock_code=obj.stock_code)
            return stock.stock_name
        except Stock.DoesNotExist:
            return None
    
    def get_current_price(self, obj):
        latest_data = StockDailyData.objects.filter(
            stock_code=obj.stock_code
        ).order_by('-trade_date').first()
        return latest_data.close_price if latest_data else None
    
    def get_profit_since_favorite(self, obj):
        return obj.get_profit_since_favorite()


class StockChartDataSerializer(serializers.Serializer):
    """股票图表数据序列化器"""
    datetime = serializers.DateTimeField()
    open_price = serializers.DecimalField(max_digits=10, decimal_places=3)
    close_price = serializers.DecimalField(max_digits=10, decimal_places=3)
    high_price = serializers.DecimalField(max_digits=10, decimal_places=3)
    low_price = serializers.DecimalField(max_digits=10, decimal_places=3)
    volume = serializers.IntegerField()
    change_rate = serializers.DecimalField(max_digits=8, decimal_places=4)
