#!/usr/bin/env python
"""
安装MySQL版本股票系统所需的依赖包
"""

import subprocess
import sys
import os

def install_package(package):
    """安装单个包"""
    try:
        print(f"正在安装 {package}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✓ {package} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package} 安装失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("MySQL版本股票系统依赖安装程序")
    print("=" * 60)
    
    # 需要安装的包列表
    packages = [
        "Django==4.2.0",
        "djangorestframework==3.14.0",
        "mysqlclient==2.1.1",
        "pymysql==1.1.0",
        "akshare==1.17.25",
        "tushare==1.2.89",
        "pandas==2.0.3",
        "numpy==1.24.3",
        "requests==2.31.0",
        "python-dateutil==2.8.2",
        "matplotlib==3.7.2"
    ]
    
    success_count = 0
    failed_packages = []
    
    for package in packages:
        if install_package(package):
            success_count += 1
        else:
            failed_packages.append(package)
    
    print("\n" + "=" * 60)
    print("安装结果:")
    print(f"成功安装: {success_count}/{len(packages)} 个包")
    
    if failed_packages:
        print("失败的包:")
        for package in failed_packages:
            print(f"  - {package}")
        print("\n请手动安装失败的包或检查网络连接")
    else:
        print("✓ 所有依赖包安装成功！")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
