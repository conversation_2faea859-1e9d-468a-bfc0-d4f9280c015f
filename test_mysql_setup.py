#!/usr/bin/env python
"""
测试MySQL版本股票系统的设置
"""

import os
import sys
import django
import pymysql
from datetime import datetime

# 添加Django项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock_management.settings')

def test_mysql_connection():
    """测试MySQL连接"""
    print("1. 测试MySQL连接...")
    try:
        connection = pymysql.connect(
            host='localhost',
            user='root',
            password='123456',
            database='stock_management',
            charset='utf8mb4'
        )
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"   ✓ MySQL连接成功，版本: {version[0]}")
        cursor.close()
        connection.close()
        return True
    except Exception as e:
        print(f"   ❌ MySQL连接失败: {e}")
        return False

def test_django_setup():
    """测试Django设置"""
    print("2. 测试Django设置...")
    try:
        django.setup()
        from django.conf import settings
        print(f"   ✓ Django设置成功")
        print(f"   ✓ 数据库引擎: {settings.DATABASES['default']['ENGINE']}")
        print(f"   ✓ 数据库名称: {settings.DATABASES['default']['NAME']}")
        return True
    except Exception as e:
        print(f"   ❌ Django设置失败: {e}")
        return False

def test_models():
    """测试模型导入"""
    print("3. 测试模型导入...")
    try:
        from stocks.models import Stock, StockDailyData, StockMinuteData, HS300Data
        print("   ✓ 股票模型导入成功")

        from trading.models import TradingOrder, UserPosition, TradingRecord
        print("   ✓ 交易模型导入成功")

        from strategies.models import TradingStrategy, StrategyExecution, GridLevel
        print("   ✓ 策略模型导入成功")

        return True
    except Exception as e:
        print(f"   ❌ 模型导入失败: {e}")
        return False

def test_database_tables():
    """测试数据库表"""
    print("4. 测试数据库表...")
    try:
        from stocks.models import Stock
        
        # 测试表是否存在
        stock_count = Stock.objects.count()
        print(f"   ✓ 股票表访问成功，当前有 {stock_count} 条记录")
        
        return True
    except Exception as e:
        print(f"   ❌ 数据库表测试失败: {e}")
        print("   提示: 请先运行 'python manage.py migrate' 创建数据库表")
        return False

def test_dependencies():
    """测试依赖包"""
    print("5. 测试依赖包...")
    
    required_packages = [
        ('django', 'Django'),
        ('rest_framework', 'Django REST Framework'),
        ('pymysql', 'PyMySQL'),
        ('akshare', 'AKShare'),
        ('tushare', 'Tushare'),
        ('pandas', 'Pandas'),
        ('matplotlib', 'Matplotlib')
    ]
    
    missing_packages = []
    
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"   ✓ {name} 已安装")
        except ImportError:
            print(f"   ❌ {name} 未安装")
            missing_packages.append(name)
    
    if missing_packages:
        print(f"\n   缺少依赖包: {', '.join(missing_packages)}")
        print("   请运行: python install_dependencies.py")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("MySQL版本股票系统设置测试")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    tests = [
        test_dependencies,
        test_mysql_connection,
        test_django_setup,
        test_models,
        test_database_tables
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            print()
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统配置正确。")
        print("\n下一步:")
        print("1. 运行数据爬取: python scripts/mysql_stock_fetcher.py")
        print("2. 启动服务器: python manage.py runserver")
    else:
        print("⚠️  部分测试失败，请检查配置。")
        print("\n常见问题解决:")
        print("1. 安装依赖: python install_dependencies.py")
        print("2. 创建数据库: CREATE DATABASE stock_management;")
        print("3. 执行迁移: python manage.py migrate")
    
    print("=" * 60)

if __name__ == '__main__':
    main()
