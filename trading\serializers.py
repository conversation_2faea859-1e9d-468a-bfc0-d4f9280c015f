from rest_framework import serializers
from .models import TradingOrder, UserPosition, TradingRecord
from stocks.models import Stock


class TradingOrderSerializer(serializers.ModelSerializer):
    """交易委托序列化器"""
    stock_name = serializers.SerializerMethodField()
    order_type_display = serializers.CharField(source='get_order_type_display', read_only=True)
    order_status_display = serializers.CharField(source='get_order_status_display', read_only=True)
    remaining_quantity = serializers.ReadOnlyField()
    
    class Meta:
        model = TradingOrder
        fields = ('id', 'stock_code', 'stock_name', 'order_type', 'order_type_display',
                 'order_price', 'order_quantity', 'filled_quantity', 'remaining_quantity',
                 'order_status', 'order_status_display', 'order_time', 'filled_time',
                 'commission_fee', 'strategy_id')
        read_only_fields = ('id', 'filled_quantity', 'order_status', 'filled_time', 'commission_fee')
    
    def get_stock_name(self, obj):
        try:
            stock = Stock.objects.get(stock_code=obj.stock_code)
            return stock.stock_name
        except Stock.DoesNotExist:
            return None


class CreateOrderSerializer(serializers.Serializer):
    """创建委托序列化器"""
    stock_code = serializers.CharField(max_length=10)
    order_type = serializers.ChoiceField(choices=TradingOrder.ORDER_TYPE_CHOICES)
    order_price = serializers.DecimalField(max_digits=10, decimal_places=3, min_value=0.001)
    order_quantity = serializers.IntegerField(min_value=1)
    
    def validate_stock_code(self, value):
        try:
            Stock.objects.get(stock_code=value)
            return value
        except Stock.DoesNotExist:
            raise serializers.ValidationError("股票代码不存在")


class UserPositionSerializer(serializers.ModelSerializer):
    """用户持仓序列化器"""
    stock_name = serializers.SerializerMethodField()
    
    class Meta:
        model = UserPosition
        fields = ('id', 'stock_code', 'stock_name', 'total_quantity', 'available_quantity',
                 'avg_cost_price', 'total_cost', 'current_price', 'market_value',
                 'profit_loss', 'profit_loss_rate', 'first_buy_time', 'last_update_time')
        read_only_fields = ('id', 'last_update_time')
    
    def get_stock_name(self, obj):
        try:
            stock = Stock.objects.get(stock_code=obj.stock_code)
            return stock.stock_name
        except Stock.DoesNotExist:
            return None


class TradingRecordSerializer(serializers.ModelSerializer):
    """交易记录序列化器"""
    stock_name = serializers.SerializerMethodField()
    trade_type_display = serializers.CharField(source='get_trade_type_display', read_only=True)
    
    class Meta:
        model = TradingRecord
        fields = ('id', 'stock_code', 'stock_name', 'trade_type', 'trade_type_display',
                 'trade_price', 'trade_quantity', 'trade_amount', 'commission_fee',
                 'stamp_duty', 'total_fee', 'net_amount', 'trade_time', 'strategy_id')
        read_only_fields = ('id', 'trade_amount', 'commission_fee', 'stamp_duty', 'total_fee', 'net_amount')
    
    def get_stock_name(self, obj):
        try:
            stock = Stock.objects.get(stock_code=obj.stock_code)
            return stock.stock_name
        except Stock.DoesNotExist:
            return None


class ProfitSummarySerializer(serializers.Serializer):
    """收益汇总序列化器"""
    total_assets = serializers.DecimalField(max_digits=15, decimal_places=2)
    current_capital = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_market_value = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_profit = serializers.DecimalField(max_digits=15, decimal_places=2)
    total_profit_rate = serializers.DecimalField(max_digits=8, decimal_places=4)
    realized_profit = serializers.DecimalField(max_digits=15, decimal_places=2)
    unrealized_profit = serializers.DecimalField(max_digits=15, decimal_places=2)
