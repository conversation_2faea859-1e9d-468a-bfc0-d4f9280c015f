# Generated by Django 4.2 on 2025-07-29 06:42

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TradingOrder',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('order_type', models.IntegerField(choices=[(1, '买入'), (2, '卖出')], verbose_name='委托类型')),
                ('order_price', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='委托价格')),
                ('order_quantity', models.IntegerField(verbose_name='委托数量')),
                ('filled_quantity', models.IntegerField(default=0, verbose_name='已成交数量')),
                ('order_status', models.IntegerField(choices=[(1, '待成交'), (2, '部分成交'), (3, '全部成交'), (4, '已撤销')], default=1, verbose_name='委托状态')),
                ('order_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='委托时间')),
                ('filled_time', models.DateTimeField(blank=True, null=True, verbose_name='成交时间')),
                ('commission_fee', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='手续费')),
                ('strategy_id', models.IntegerField(blank=True, null=True, verbose_name='关联策略ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '交易委托',
                'verbose_name_plural': '交易委托',
                'db_table': 'trading_orders',
            },
        ),
        migrations.CreateModel(
            name='UserPosition',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('total_quantity', models.IntegerField(default=0, verbose_name='总持仓数量')),
                ('available_quantity', models.IntegerField(default=0, verbose_name='可用数量')),
                ('avg_cost_price', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='平均成本价')),
                ('total_cost', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='总成本')),
                ('current_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='当前价格')),
                ('market_value', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='市值')),
                ('profit_loss', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='盈亏')),
                ('profit_loss_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='盈亏率')),
                ('first_buy_time', models.DateTimeField(blank=True, null=True, verbose_name='首次买入时间')),
                ('last_update_time', models.DateTimeField(auto_now=True, verbose_name='最后更新时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户持仓',
                'verbose_name_plural': '用户持仓',
                'db_table': 'user_positions',
            },
        ),
        migrations.CreateModel(
            name='TradingRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('trade_type', models.IntegerField(choices=[(1, '买入'), (2, '卖出')], verbose_name='交易类型')),
                ('trade_price', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='成交价格')),
                ('trade_quantity', models.IntegerField(verbose_name='成交数量')),
                ('trade_amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='成交金额')),
                ('commission_fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='手续费')),
                ('stamp_duty', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10, verbose_name='印花税')),
                ('total_fee', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='总费用')),
                ('net_amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='净金额')),
                ('trade_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='成交时间')),
                ('strategy_id', models.IntegerField(blank=True, null=True, verbose_name='关联策略ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('order', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='trading.tradingorder', verbose_name='关联委托')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '交易记录',
                'verbose_name_plural': '交易记录',
                'db_table': 'trading_records',
            },
        ),
        migrations.AddIndex(
            model_name='userposition',
            index=models.Index(fields=['user'], name='user_positi_user_id_876b08_idx'),
        ),
        migrations.AddIndex(
            model_name='userposition',
            index=models.Index(fields=['stock_code'], name='user_positi_stock_c_a4b4c8_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userposition',
            unique_together={('user', 'stock_code')},
        ),
        migrations.AddIndex(
            model_name='tradingrecord',
            index=models.Index(fields=['user'], name='trading_rec_user_id_f0a17f_idx'),
        ),
        migrations.AddIndex(
            model_name='tradingrecord',
            index=models.Index(fields=['stock_code'], name='trading_rec_stock_c_e21553_idx'),
        ),
        migrations.AddIndex(
            model_name='tradingrecord',
            index=models.Index(fields=['trade_time'], name='trading_rec_trade_t_ae3ddb_idx'),
        ),
        migrations.AddIndex(
            model_name='tradingrecord',
            index=models.Index(fields=['strategy_id'], name='trading_rec_strateg_310118_idx'),
        ),
        migrations.AddIndex(
            model_name='tradingorder',
            index=models.Index(fields=['user'], name='trading_ord_user_id_ce3262_idx'),
        ),
        migrations.AddIndex(
            model_name='tradingorder',
            index=models.Index(fields=['stock_code'], name='trading_ord_stock_c_3f93b2_idx'),
        ),
        migrations.AddIndex(
            model_name='tradingorder',
            index=models.Index(fields=['order_status'], name='trading_ord_order_s_82a7c4_idx'),
        ),
        migrations.AddIndex(
            model_name='tradingorder',
            index=models.Index(fields=['strategy_id'], name='trading_ord_strateg_a61e29_idx'),
        ),
    ]
