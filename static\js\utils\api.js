// API工具类
const API = {
    baseURL: '/api',
    
    // 获取CSRF token
    getCsrfToken() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'csrftoken') {
                return value;
            }
        }
        return null;
    },

    // 通用请求方法
    async request(method, url, data = null) {
        try {
            const headers = {
                'Content-Type': 'application/json',
            };

            // 添加CSRF token
            const csrfToken = this.getCsrfToken();
            if (csrfToken) {
                headers['X-CSRFToken'] = csrfToken;
            }

            const config = {
                method: method,
                url: this.baseURL + url,
                headers: headers,
                withCredentials: true,  // 确保发送cookies
            };

            if (data) {
                config.data = data;
            }

            const response = await axios(config);
            return { success: true, data: response.data };
        } catch (error) {
            console.error('API请求错误:', error);
            const message = error.response?.data?.error || error.response?.data?.detail || error.response?.data?.message || error.message || '请求失败';
            return { success: false, error: message };
        }
    },
    
    // GET请求
    get(url) {
        return this.request('GET', url);
    },
    
    // POST请求
    post(url, data) {
        return this.request('POST', url, data);
    },
    
    // PUT请求
    put(url, data) {
        return this.request('PUT', url, data);
    },
    
    // DELETE请求
    delete(url) {
        return this.request('DELETE', url);
    },
    
    // 用户认证相关API
    auth: {
        // 用户登录
        login(username, password) {
            return API.post('/accounts/login/', { username, password });
        },
        
        // 用户注册
        register(userData) {
            return API.post('/accounts/register/', userData);
        },
        
        // 用户登出
        logout() {
            return API.post('/accounts/logout/', {});
        },
        
        // 获取用户资料
        getProfile() {
            return API.get('/accounts/profile/');
        },
        
        // 修改密码
        changePassword(oldPassword, newPassword, newPasswordConfirm) {
            return API.post('/accounts/change-password/', {
                old_password: oldPassword,
                new_password: newPassword,
                new_password_confirm: newPasswordConfirm
            });
        }
    },
    
    // 股票相关API
    stocks: {
        // 获取股票排行
        getRanking(period = '1Y', limit = 20) {
            return API.get(`/stocks/ranking/?period=${period}&limit=${limit}`);
        },
        
        // 搜索股票
        search(keyword) {
            return API.get(`/stocks/search/?keyword=${encodeURIComponent(keyword)}`);
        },
        
        // 获取股票详情
        getDetail(stockCode) {
            return API.get(`/stocks/${stockCode}/`);
        },
        
        // 获取股票图表数据
        getChartData(stockCode, period = '1D') {
            return API.get(`/stocks/${stockCode}/chart/?period=${period}`);
        },
        
        // 获取沪深300图表数据
        getHS300ChartData(period = '1D') {
            return API.get(`/stocks/hs300/chart/?period=${period}`);
        },
        
        // 获取股票期间涨跌幅
        getPeriodChanges(stockCode) {
            return API.get(`/stocks/${stockCode}/changes/`);
        },
        
        // 获取当前价格
        getCurrentPrice(stockCode) {
            return API.get(`/stocks/${stockCode}/price/`);
        }
    },
    
    // 收藏相关API
    favorites: {
        // 获取收藏列表
        getList() {
            return API.get('/stocks/favorites/');
        },
        
        // 添加收藏
        add(stockCode) {
            return API.post('/stocks/favorites/add/', { stock_code: stockCode });
        },
        
        // 取消收藏
        remove(stockCode) {
            return API.delete(`/stocks/favorites/${stockCode}/remove/`);
        }
    },
    
    // 交易相关API
    trading: {
        // 创建委托
        createOrder(orderData) {
            return API.post('/trading/orders/create/', orderData);
        },
        
        // 获取当前委托
        getCurrentOrders() {
            return API.get('/trading/orders/current/');
        },
        
        // 获取历史委托
        getHistoryOrders() {
            return API.get('/trading/orders/history/');
        },
        
        // 撤销委托
        cancelOrder(orderId) {
            return API.post(`/trading/orders/${orderId}/cancel/`, {});
        },
        
        // 获取当前持仓
        getCurrentPositions() {
            return API.get('/trading/positions/current/');
        },
        
        // 获取历史持仓
        getHistoryPositions() {
            return API.get('/trading/positions/history/');
        },
        
        // 获取交易记录
        getTradingRecords() {
            return API.get('/trading/records/');
        },
        
        // 获取收益汇总
        getProfitSummary() {
            return API.get('/trading/profit-summary/');
        },
        
        // 快速买入
        quickBuy(stockCode, quantity) {
            return API.post(`/trading/${stockCode}/quick-buy/`, { quantity });
        },
        
        // 快速卖出
        quickSell(stockCode, quantity) {
            return API.post(`/trading/${stockCode}/quick-sell/`, { quantity });
        }
    },
    
    // 策略相关API
    strategies: {
        // 创建网格策略
        createGridStrategy(strategyData) {
            return API.post('/strategies/grid/create/', strategyData);
        },
        
        // 创建马丁格尔策略
        createMartinStrategy(strategyData) {
            return API.post('/strategies/martin/create/', strategyData);
        },
        
        // 获取运行中的策略
        getRunningStrategies() {
            return API.get('/strategies/running/');
        },
        
        // 获取历史策略
        getHistoryStrategies() {
            return API.get('/strategies/history/');
        },

        // 获取策略详情
        getStrategyDetail(strategyId) {
            return API.get(`/strategies/${strategyId}/`);
        },
        
        // 停止策略
        stopStrategy(strategyId) {
            return API.post(`/strategies/${strategyId}/stop/`, {});
        },
        
        // 修改策略名称
        updateStrategyName(strategyId, strategyName) {
            return API.post(`/strategies/${strategyId}/update-name/`, { strategy_name: strategyName });
        },
        
        // 获取策略执行记录
        getStrategyExecutions(strategyId) {
            return API.get(`/strategies/${strategyId}/executions/`);
        },
        
        // 获取策略汇总
        getStrategySummary() {
            return API.get('/strategies/summary/');
        }
    },
    
    // 管理员相关API
    admin: {
        // 获取用户列表
        getUserList() {
            return API.get('/accounts/admin/users/');
        },
        
        // 获取用户详情
        getUserDetail(userId) {
            return API.get(`/accounts/admin/users/${userId}/`);
        },
        
        // 删除用户
        deleteUser(userId) {
            return API.delete(`/accounts/admin/users/${userId}/delete/`);
        },
        
        // 重置用户密码
        resetUserPassword(userId, newPassword) {
            return API.post(`/accounts/admin/users/${userId}/reset-password/`, { new_password: newPassword });
        }
    }
};

// 工具函数
const Utils = {
    // 格式化数字
    formatNumber(num, decimals = 2) {
        if (num === null || num === undefined || num === '--') return '--';
        return Number(num).toFixed(decimals);
    },
    
    // 格式化百分比
    formatPercent(num, decimals = 2) {
        if (num === null || num === undefined || num === '--') return '--';
        return Number(num).toFixed(decimals) + '%';
    },
    
    // 格式化金额
    formatMoney(num, decimals = 2) {
        if (num === null || num === undefined || num === '--') return '--';
        return '¥' + Number(num).toLocaleString('zh-CN', { minimumFractionDigits: decimals, maximumFractionDigits: decimals });
    },
    
    // 格式化日期时间
    formatDateTime(dateStr) {
        if (!dateStr) return '--';
        const date = new Date(dateStr);
        return date.toLocaleString('zh-CN');
    },
    
    // 格式化日期
    formatDate(dateStr) {
        if (!dateStr) return '--';
        const date = new Date(dateStr);
        return date.toLocaleDateString('zh-CN');
    },
    
    // 获取涨跌颜色类
    getPriceColorClass(value) {
        if (value > 0) return 'price-up';
        if (value < 0) return 'price-down';
        return 'price-flat';
    },
    
    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
};
