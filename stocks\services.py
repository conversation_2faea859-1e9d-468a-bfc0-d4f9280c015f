import akshare as ak
import tushare as ts
import pandas as pd
from datetime import datetime, timedelta, date
from django.conf import settings
from django.db import transaction, models
from .models import Stock, StockDailyData, StockMinuteData, HS300Data
import logging
import decimal

logger = logging.getLogger(__name__)


class StockDataService:
    """股票数据服务"""
    
    def __init__(self):
        # 初始化tushare
        ts.set_token(settings.STOCK_DATA_CONFIG['TUSHARE_TOKEN'])
        self.pro = ts.pro_api()
    
    def get_stock_ranking(self, period='1Y', limit=20):
        """获取股票涨幅排行"""
        try:
            # 计算时间范围
            end_date = datetime.now().date()
            if period == '1Y':
                start_date = end_date - timedelta(days=365)
            elif period == '1M':
                start_date = end_date - timedelta(days=30)
            else:
                start_date = end_date - timedelta(days=365)
            
            # 从数据库获取股票涨幅数据
            ranking_data = []
            stocks = Stock.objects.filter(is_active=True)
            
            for stock in stocks:
                # 获取期间开始和结束的价格
                start_data = StockDailyData.objects.filter(
                    stock_code=stock.stock_code,
                    trade_date__gte=start_date
                ).order_by('trade_date').first()
                
                end_data = StockDailyData.objects.filter(
                    stock_code=stock.stock_code,
                    trade_date__lte=end_date
                ).order_by('-trade_date').first()
                
                if start_data and end_data and start_data.close_price and start_data.close_price > 0:
                    try:
                        # 计算涨幅，确保数值有效
                        change_amount = end_data.close_price - start_data.close_price
                        change_rate = (change_amount / start_data.close_price) * 100

                        # 确保所有数值都是有效的
                        if change_rate is not None and str(change_rate) not in ['inf', '-inf', 'nan']:
                            ranking_data.append({
                                'stock_code': stock.stock_code,
                                'stock_name': stock.stock_name,
                                'latest_price': float(end_data.close_price),
                                'change_rate': float(change_rate),
                                'change_amount': float(change_amount),
                                'volume': end_data.volume or 0,
                                'market': stock.market,
                            })
                    except (ZeroDivisionError, ValueError, TypeError, decimal.InvalidOperation):
                        # 跳过无效数据
                        continue
            
            # 按涨幅排序
            ranking_data.sort(key=lambda x: x['change_rate'], reverse=True)
            return ranking_data[:limit]
            
        except Exception as e:
            logger.error(f"获取股票排行失败: {e}")
            return []
    
    def search_stock(self, keyword):
        """搜索股票"""
        try:
            from django.db.models import Q
            stocks = Stock.objects.filter(
                Q(stock_code__icontains=keyword) |
                Q(stock_name__icontains=keyword)
            ).filter(is_active=True)
            return stocks
        except Exception as e:
            logger.error(f"搜索股票失败: {e}")
            return Stock.objects.none()
    
    def get_stock_chart_data(self, stock_code, period='1D'):
        """获取股票图表数据"""
        try:
            end_time = datetime.now()
            
            if period == '1min':
                # 1分钟数据
                start_time = end_time - timedelta(minutes=240)  # 4小时
                data = StockMinuteData.objects.filter(
                    stock_code=stock_code,
                    datetime__gte=start_time,
                    datetime__lte=end_time
                ).order_by('datetime')
            elif period == '1D':
                # 1日数据
                start_time = end_time - timedelta(days=1)
                data = StockMinuteData.objects.filter(
                    stock_code=stock_code,
                    datetime__gte=start_time,
                    datetime__lte=end_time
                ).order_by('datetime')
            elif period == '1W':
                # 1周数据
                start_date = end_time.date() - timedelta(days=7)
                data = StockDailyData.objects.filter(
                    stock_code=stock_code,
                    trade_date__gte=start_date,
                    trade_date__lte=end_time.date()
                ).order_by('trade_date')
            elif period == '1M':
                # 1月数据
                start_date = end_time.date() - timedelta(days=30)
                data = StockDailyData.objects.filter(
                    stock_code=stock_code,
                    trade_date__gte=start_date,
                    trade_date__lte=end_time.date()
                ).order_by('trade_date')
            elif period == '3M':
                # 3月数据
                start_date = end_time.date() - timedelta(days=90)
                data = StockDailyData.objects.filter(
                    stock_code=stock_code,
                    trade_date__gte=start_date,
                    trade_date__lte=end_time.date()
                ).order_by('trade_date')
            elif period == '6M':
                # 6月数据
                start_date = end_time.date() - timedelta(days=180)
                data = StockDailyData.objects.filter(
                    stock_code=stock_code,
                    trade_date__gte=start_date,
                    trade_date__lte=end_time.date()
                ).order_by('trade_date')
            else:
                data = []
            
            return data
            
        except Exception as e:
            logger.error(f"获取股票图表数据失败: {e}")
            return []
    
    def get_hs300_chart_data(self, period='1D'):
        """获取沪深300图表数据"""
        try:
            end_time = datetime.now()

            if period in ['1min', '1D']:
                # 分钟数据 - 与股票数据保持一致的时间范围
                if period == '1min':
                    start_time = end_time - timedelta(minutes=240)  # 4小时
                else:  # 1D
                    start_time = end_time - timedelta(days=1)  # 1天

                # 使用datetime范围查询，与股票数据逻辑一致
                data = HS300Data.objects.filter(
                    data_type=2,  # 分钟线
                    datetime__gte=start_time,
                    datetime__lte=end_time
                ).order_by('datetime')

                # 如果指定时间范围没有数据，获取最近的分钟数据
                if not data.exists():
                    data = HS300Data.objects.filter(
                        data_type=2
                    ).order_by('-datetime')[:240]  # 最近240条分钟数据
            else:
                # 日线数据 - 与股票数据保持一致的时间范围
                if period == '1W':
                    start_date = end_time.date() - timedelta(days=7)
                elif period == '1M':
                    start_date = end_time.date() - timedelta(days=30)
                elif period == '3M':
                    start_date = end_time.date() - timedelta(days=90)
                elif period == '6M':
                    start_date = end_time.date() - timedelta(days=180)
                else:
                    start_date = end_time.date() - timedelta(days=30)

                data = HS300Data.objects.filter(
                    data_type=1,  # 日线
                    trade_date__gte=start_date,
                    trade_date__lte=end_time.date()
                ).order_by('trade_date')

            return data

        except Exception as e:
            logger.error(f"获取沪深300图表数据失败: {e}")
            return []
    
    def get_current_price(self, stock_code):
        """获取股票当前价格"""
        try:
            # 先尝试从当日分钟数据获取最新价格
            latest_minute = StockMinuteData.objects.filter(
                stock_code=stock_code,
                data_type=2  # 当日实时数据
            ).order_by('-datetime').first()
            
            if latest_minute:
                return latest_minute.close_price
            
            # 如果没有分钟数据，从日线数据获取
            latest_daily = StockDailyData.objects.filter(
                stock_code=stock_code
            ).order_by('-trade_date').first()
            
            return latest_daily.close_price if latest_daily else None
            
        except Exception as e:
            logger.error(f"获取当前价格失败: {e}")
            return None
    
    def calculate_period_change(self, stock_code, period):
        """计算指定期间的涨跌幅"""
        try:
            end_time = datetime.now()
            
            if period == '1min':
                start_time = end_time - timedelta(minutes=1)
                current_data = StockMinuteData.objects.filter(
                    stock_code=stock_code,
                    datetime__lte=end_time
                ).order_by('-datetime').first()
                
                start_data = StockMinuteData.objects.filter(
                    stock_code=stock_code,
                    datetime__lte=start_time
                ).order_by('-datetime').first()
            else:
                # 其他期间使用日线数据
                if period == '1D':
                    start_date = end_time.date() - timedelta(days=1)
                elif period == '1W':
                    start_date = end_time.date() - timedelta(days=7)
                elif period == '1M':
                    start_date = end_time.date() - timedelta(days=30)
                elif period == '3M':
                    start_date = end_time.date() - timedelta(days=90)
                elif period == '6M':
                    start_date = end_time.date() - timedelta(days=180)
                else:
                    return None
                
                current_data = StockDailyData.objects.filter(
                    stock_code=stock_code,
                    trade_date__lte=end_time.date()
                ).order_by('-trade_date').first()
                
                start_data = StockDailyData.objects.filter(
                    stock_code=stock_code,
                    trade_date__gte=start_date
                ).order_by('trade_date').first()
            
            if current_data and start_data:
                change_rate = ((current_data.close_price - start_data.close_price) / start_data.close_price) * 100
                change_amount = current_data.close_price - start_data.close_price
                return {
                    'change_rate': change_rate,
                    'change_amount': change_amount
                }
            
            return None
            
        except Exception as e:
            logger.error(f"计算期间涨跌幅失败: {e}")
            return None
