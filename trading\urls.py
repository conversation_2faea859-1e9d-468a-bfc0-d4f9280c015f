from django.urls import path
from . import views

app_name = 'trading'

urlpatterns = [
    # 委托管理
    path('orders/create/', views.CreateOrderView.as_view(), name='create_order'),
    path('orders/current/', views.CurrentOrdersView.as_view(), name='current_orders'),
    path('orders/history/', views.HistoryOrdersView.as_view(), name='history_orders'),
    path('orders/<int:order_id>/cancel/', views.CancelOrderView.as_view(), name='cancel_order'),
    
    # 持仓管理
    path('positions/current/', views.CurrentPositionsView.as_view(), name='current_positions'),
    path('positions/history/', views.HistoryPositionsView.as_view(), name='history_positions'),
    
    # 交易记录
    path('records/', views.TradingRecordsView.as_view(), name='trading_records'),
    
    # 收益汇总
    path('profit-summary/', views.ProfitSummaryView.as_view(), name='profit_summary'),
    
    # 快速交易
    path('<str:stock_code>/quick-buy/', views.quick_buy, name='quick_buy'),
    path('<str:stock_code>/quick-sell/', views.quick_sell, name='quick_sell'),
]
