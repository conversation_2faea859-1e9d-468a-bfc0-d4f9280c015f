# Generated by Django 4.2 on 2025-07-29 06:42

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='HS300Data',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('trade_date', models.DateField(db_index=True, verbose_name='交易日期')),
                ('datetime', models.DateTimeField(blank=True, db_index=True, null=True, verbose_name='具体时间')),
                ('close_price', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='收盘价')),
                ('change_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='涨跌幅')),
                ('data_type', models.IntegerField(choices=[(1, '日线'), (2, '分钟线')], default=1, verbose_name='数据类型')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '沪深300数据',
                'verbose_name_plural': '沪深300数据',
                'db_table': 'hs300_data',
            },
        ),
        migrations.CreateModel(
            name='Stock',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(max_length=10, unique=True, verbose_name='股票代码')),
                ('stock_name', models.CharField(max_length=50, verbose_name='股票名称')),
                ('market', models.CharField(choices=[('SH', '上海'), ('SZ', '深圳')], max_length=10, verbose_name='市场')),
                ('industry', models.CharField(blank=True, max_length=50, null=True, verbose_name='行业')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否活跃')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '股票',
                'verbose_name_plural': '股票',
                'db_table': 'stocks',
            },
        ),
        migrations.CreateModel(
            name='StockDailyData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('trade_date', models.DateField(db_index=True, verbose_name='交易日期')),
                ('open_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='开盘价')),
                ('close_price', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='收盘价')),
                ('high_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='最高价')),
                ('low_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='最低价')),
                ('volume', models.BigIntegerField(blank=True, null=True, verbose_name='成交量')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='成交额')),
                ('change_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='涨跌幅')),
                ('change_amount', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='涨跌额')),
                ('turnover_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='换手率')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '股票日线数据',
                'verbose_name_plural': '股票日线数据',
                'db_table': 'stock_daily_data',
            },
        ),
        migrations.CreateModel(
            name='StockMinuteData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('datetime', models.DateTimeField(db_index=True, verbose_name='时间')),
                ('open_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='开盘价')),
                ('close_price', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='收盘价')),
                ('high_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='最高价')),
                ('low_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='最低价')),
                ('volume', models.BigIntegerField(blank=True, null=True, verbose_name='成交量')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='成交额')),
                ('change_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='涨跌幅')),
                ('change_amount', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='涨跌额')),
                ('turnover_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='换手率')),
                ('data_type', models.IntegerField(choices=[(1, '历史分钟数据'), (2, '当日实时数据')], default=1, verbose_name='数据类型')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
            ],
            options={
                'verbose_name': '股票分钟数据',
                'verbose_name_plural': '股票分钟数据',
                'db_table': 'stock_minute_data',
            },
        ),
        migrations.CreateModel(
            name='UserFavorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('favorite_date', models.DateField(verbose_name='收藏日期')),
                ('favorite_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='收藏时价格')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '用户收藏',
                'verbose_name_plural': '用户收藏',
                'db_table': 'user_favorites',
            },
        ),
        migrations.AddIndex(
            model_name='stockminutedata',
            index=models.Index(fields=['stock_code', 'datetime'], name='stock_minut_stock_c_8e3175_idx'),
        ),
        migrations.AddIndex(
            model_name='stockminutedata',
            index=models.Index(fields=['datetime'], name='stock_minut_datetim_3f384d_idx'),
        ),
        migrations.AddIndex(
            model_name='stockminutedata',
            index=models.Index(fields=['data_type'], name='stock_minut_data_ty_3b0f74_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stockminutedata',
            unique_together={('stock_code', 'datetime')},
        ),
        migrations.AddIndex(
            model_name='stockdailydata',
            index=models.Index(fields=['stock_code', 'trade_date'], name='stock_daily_stock_c_b6a131_idx'),
        ),
        migrations.AddIndex(
            model_name='stockdailydata',
            index=models.Index(fields=['trade_date'], name='stock_daily_trade_d_59ad44_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='stockdailydata',
            unique_together={('stock_code', 'trade_date')},
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['stock_code'], name='stocks_stock_c_fb14da_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['stock_name'], name='stocks_stock_n_b91d29_idx'),
        ),
        migrations.AddIndex(
            model_name='stock',
            index=models.Index(fields=['market'], name='stocks_market_143d55_idx'),
        ),
        migrations.AddIndex(
            model_name='hs300data',
            index=models.Index(fields=['trade_date'], name='hs300_data_trade_d_945a94_idx'),
        ),
        migrations.AddIndex(
            model_name='hs300data',
            index=models.Index(fields=['datetime'], name='hs300_data_datetim_356d92_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='hs300data',
            unique_together={('trade_date', 'datetime', 'data_type')},
        ),
        migrations.AddIndex(
            model_name='userfavorite',
            index=models.Index(fields=['user'], name='user_favori_user_id_216ac3_idx'),
        ),
        migrations.AddIndex(
            model_name='userfavorite',
            index=models.Index(fields=['stock_code'], name='user_favori_stock_c_9b5573_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='userfavorite',
            unique_together={('user', 'stock_code')},
        ),
    ]
