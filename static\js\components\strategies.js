// 策略组件
Vue.component('strategies-component', {
    template: `
        <div class="strategies-component">
            <!-- 策略汇总 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ strategySummary.total_strategies }}</div>
                    <div class="stat-label">总策略数</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ strategySummary.running_strategies }}</div>
                    <div class="stat-label">运行中</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ strategySummary.completed_strategies }}</div>
                    <div class="stat-label">已完成</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" :class="getPriceColorClass(strategySummary.total_realized_profit)">
                        {{ strategySummary.total_realized_profit | formatMoney }}
                    </div>
                    <div class="stat-label">总收益</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" :class="getPriceColorClass(strategySummary.total_profit_rate)">
                        {{ strategySummary.total_profit_rate | formatPercent }}
                    </div>
                    <div class="stat-label">总收益率</div>
                </div>
            </div>

            <!-- 创建策略按钮 -->
            <div class="button-group">
                <el-button type="primary" @click="showCreateGridDialog">创建网格策略</el-button>
                <el-button type="success" @click="showCreateMartinDialog">创建马丁格尔策略</el-button>
                <el-button @click="refreshData" icon="el-icon-refresh">刷新</el-button>
            </div>

            <!-- 策略列表 -->
            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
                <!-- 运行中的策略 -->
                <el-tab-pane label="运行中的策略" name="running">
                    <div class="data-table">
                        <el-table :data="runningStrategies" style="width: 100%">
                            <el-table-column prop="strategy_name" label="策略名称" width="150"></el-table-column>
                            <el-table-column prop="strategy_type_display" label="类型" width="100"></el-table-column>
                            <el-table-column prop="stock_code" label="股票代码" width="100"></el-table-column>
                            <el-table-column prop="stock_name" label="股票名称" width="120"></el-table-column>
                            <el-table-column prop="total_investment" label="投资额" width="120">
                                <template slot-scope="scope">
                                    {{ scope.row.total_investment | formatMoney }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="realized_profit" label="已实现收益" width="120">
                                <template slot-scope="scope">
                                    <span :class="getPriceColorClass(scope.row.realized_profit)">
                                        {{ scope.row.realized_profit | formatMoney }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="realized_profit_rate" label="收益率" width="100">
                                <template slot-scope="scope">
                                    <span :class="getPriceColorClass(scope.row.realized_profit_rate)">
                                        {{ scope.row.realized_profit_rate | formatPercent }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="running_time_display" label="运行时间" width="150"></el-table-column>
                            <el-table-column label="操作" width="150">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="viewStrategyDetail(scope.row.id)">详情</el-button>
                                    <el-button size="mini" type="danger" @click="stopStrategy(scope.row.id)">停止</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <!-- 历史策略 -->
                <el-tab-pane label="历史策略" name="history">
                    <div class="data-table">
                        <el-table :data="historyStrategies" style="width: 100%">
                            <el-table-column prop="strategy_name" label="策略名称" width="150"></el-table-column>
                            <el-table-column prop="strategy_type_display" label="类型" width="100"></el-table-column>
                            <el-table-column prop="stock_code" label="股票代码" width="100"></el-table-column>
                            <el-table-column prop="stock_name" label="股票名称" width="120"></el-table-column>
                            <el-table-column prop="status_display" label="状态" width="100"></el-table-column>
                            <el-table-column prop="total_investment" label="投资额" width="120">
                                <template slot-scope="scope">
                                    {{ scope.row.total_investment | formatMoney }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="realized_profit" label="已实现收益" width="120">
                                <template slot-scope="scope">
                                    <span :class="getPriceColorClass(scope.row.realized_profit)">
                                        {{ scope.row.realized_profit | formatMoney }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="realized_profit_rate" label="收益率" width="100">
                                <template slot-scope="scope">
                                    <span :class="getPriceColorClass(scope.row.realized_profit_rate)">
                                        {{ scope.row.realized_profit_rate | formatPercent }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="viewStrategyDetail(scope.row.id)">详情</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
            </el-tabs>

            <!-- 创建网格策略对话框 -->
            <el-dialog title="创建网格策略" :visible.sync="createGridDialogVisible" width="500px">
                <el-form :model="gridForm" :rules="gridRules" ref="gridForm" label-width="120px">
                    <el-form-item label="策略名称" prop="strategy_name">
                        <el-input v-model="gridForm.strategy_name" placeholder="请输入策略名称"></el-input>
                    </el-form-item>
                    <el-form-item label="股票代码" prop="stock_code">
                        <el-input v-model="gridForm.stock_code" placeholder="请输入股票代码"></el-input>
                    </el-form-item>
                    <el-form-item label="最低价格" prop="grid_min_price">
                        <el-input v-model="gridForm.grid_min_price" placeholder="请输入最低价格"></el-input>
                    </el-form-item>
                    <el-form-item label="最高价格" prop="grid_max_price">
                        <el-input v-model="gridForm.grid_max_price" placeholder="请输入最高价格"></el-input>
                    </el-form-item>
                    <el-form-item label="网格数量" prop="grid_count">
                        <el-input v-model="gridForm.grid_count" placeholder="请输入网格数量"></el-input>
                    </el-form-item>
                    <el-form-item label="投资金额" prop="grid_investment">
                        <el-input v-model="gridForm.grid_investment" placeholder="请输入投资金额"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button @click="createGridDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="createGridStrategy" :loading="createLoading">创建</el-button>
                </div>
            </el-dialog>

            <!-- 创建马丁格尔策略对话框 -->
            <el-dialog title="创建马丁格尔策略" :visible.sync="createMartinDialogVisible" width="500px">
                <el-form :model="martinForm" :rules="martinRules" ref="martinForm" label-width="120px">
                    <el-form-item label="策略名称" prop="strategy_name">
                        <el-input v-model="martinForm.strategy_name" placeholder="请输入策略名称"></el-input>
                    </el-form-item>
                    <el-form-item label="股票代码" prop="stock_code">
                        <el-input v-model="martinForm.stock_code" placeholder="请输入股票代码"></el-input>
                    </el-form-item>
                    <el-form-item label="触发跌幅%" prop="martin_trigger_rate">
                        <el-input v-model="martinForm.martin_trigger_rate" placeholder="请输入触发跌幅百分比"></el-input>
                    </el-form-item>
                    <el-form-item label="止盈目标%" prop="martin_profit_target">
                        <el-input v-model="martinForm.martin_profit_target" placeholder="请输入止盈目标百分比"></el-input>
                    </el-form-item>
                    <el-form-item label="初次投资额" prop="martin_initial_amount">
                        <el-input v-model="martinForm.martin_initial_amount" placeholder="请输入初次投资金额"></el-input>
                    </el-form-item>
                    <el-form-item label="加仓金额" prop="martin_add_amount">
                        <el-input v-model="martinForm.martin_add_amount" placeholder="请输入加仓金额"></el-input>
                    </el-form-item>
                    <el-form-item label="最大加仓次数" prop="martin_max_add_times">
                        <el-input v-model="martinForm.martin_max_add_times" placeholder="请输入最大加仓次数"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button @click="createMartinDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="createMartinStrategy" :loading="createLoading">创建</el-button>
                </div>
            </el-dialog>

            <!-- 策略详情对话框 -->
            <el-dialog title="策略详情" :visible.sync="detailDialogVisible" width="800px">
                <div v-if="strategyDetail">
                    <!-- 基本信息 -->
                    <el-card class="detail-card">
                        <div slot="header">
                            <span>基本信息</span>
                        </div>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <p><strong>策略名称：</strong>{{ strategyDetail.strategy_name }}</p>
                                <p><strong>策略类型：</strong>{{ strategyDetail.strategy_type_display }}</p>
                                <p><strong>股票代码：</strong>{{ strategyDetail.stock_code }}</p>
                                <p><strong>股票名称：</strong>{{ strategyDetail.stock_name }}</p>
                            </el-col>
                            <el-col :span="12">
                                <p><strong>状态：</strong>{{ strategyDetail.status_display }}</p>
                                <p><strong>创建时间：</strong>{{ formatDate(strategyDetail.created_at) }}</p>
                                <p><strong>运行时间：</strong>{{ strategyDetail.running_time_display }}</p>
                                <p><strong>总投资：</strong>¥{{ formatNumber(strategyDetail.total_investment) }}</p>
                            </el-col>
                        </el-row>
                    </el-card>

                    <!-- 收益信息 -->
                    <el-card class="detail-card" style="margin-top: 20px;">
                        <div slot="header">
                            <span>收益信息</span>
                        </div>
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <div class="profit-item">
                                    <div class="profit-label">已实现收益</div>
                                    <div class="profit-value" :class="strategyDetail.realized_profit >= 0 ? 'profit-positive' : 'profit-negative'">
                                        ¥{{ formatNumber(strategyDetail.realized_profit) }}
                                    </div>
                                </div>
                            </el-col>
                            <el-col :span="8">
                                <div class="profit-item">
                                    <div class="profit-label">收益率</div>
                                    <div class="profit-value" :class="strategyDetail.realized_profit_rate >= 0 ? 'profit-positive' : 'profit-negative'">
                                        {{ formatNumber(strategyDetail.realized_profit_rate) }}%
                                    </div>
                                </div>
                            </el-col>
                        </el-row>
                    </el-card>

                    <!-- 执行记录 -->
                    <el-card class="detail-card" style="margin-top: 20px;" v-if="strategyDetail.executions && strategyDetail.executions.length > 0">
                        <div slot="header">
                            <span>执行记录</span>
                        </div>
                        <el-table :data="strategyDetail.executions" style="width: 100%">
                            <el-table-column prop="execution_type_display" label="类型" width="100"></el-table-column>
                            <el-table-column prop="price" label="价格" width="100">
                                <template slot-scope="scope">
                                    ¥{{ formatNumber(scope.row.price) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="quantity" label="数量" width="100"></el-table-column>
                            <el-table-column prop="amount" label="金额" width="120">
                                <template slot-scope="scope">
                                    ¥{{ formatNumber(scope.row.amount) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="profit" label="收益" width="120">
                                <template slot-scope="scope">
                                    <span :class="scope.row.profit >= 0 ? 'profit-positive' : 'profit-negative'">
                                        ¥{{ formatNumber(scope.row.profit) }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="created_at" label="时间" width="150">
                                <template slot-scope="scope">
                                    {{ formatDate(scope.row.created_at) }}
                                </template>
                            </el-table-column>
                        </el-table>
                    </el-card>
                </div>
                <div slot="footer">
                    <el-button @click="detailDialogVisible = false">关闭</el-button>
                </div>
            </el-dialog>
        </div>
    `,
    
    data() {
        return {
            activeTab: 'running',
            strategySummary: {
                total_strategies: 0,
                running_strategies: 0,
                completed_strategies: 0,
                total_realized_profit: 0,
                total_profit_rate: 0
            },
            runningStrategies: [],
            historyStrategies: [],
            createGridDialogVisible: false,
            createMartinDialogVisible: false,
            createLoading: false,
            gridForm: {
                strategy_name: '',
                stock_code: '',
                grid_min_price: '',
                grid_max_price: '',
                grid_count: '',
                grid_investment: ''
            },
            gridRules: {
                strategy_name: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
                stock_code: [{ required: true, message: '请输入股票代码', trigger: 'blur' }],
                grid_min_price: [{ required: true, message: '请输入最低价格', trigger: 'blur' }],
                grid_max_price: [{ required: true, message: '请输入最高价格', trigger: 'blur' }],
                grid_count: [{ required: true, message: '请输入网格数量', trigger: 'blur' }],
                grid_investment: [{ required: true, message: '请输入投资金额', trigger: 'blur' }]
            },
            martinForm: {
                strategy_name: '',
                stock_code: '',
                martin_trigger_rate: '',
                martin_profit_target: '',
                martin_initial_amount: '',
                martin_add_amount: '',
                martin_max_add_times: ''
            },
            martinRules: {
                strategy_name: [{ required: true, message: '请输入策略名称', trigger: 'blur' }],
                stock_code: [{ required: true, message: '请输入股票代码', trigger: 'blur' }],
                martin_trigger_rate: [{ required: true, message: '请输入触发跌幅', trigger: 'blur' }],
                martin_profit_target: [{ required: true, message: '请输入止盈目标', trigger: 'blur' }],
                martin_initial_amount: [{ required: true, message: '请输入初次投资额', trigger: 'blur' }],
                martin_add_amount: [{ required: true, message: '请输入加仓金额', trigger: 'blur' }],
                martin_max_add_times: [{ required: true, message: '请输入最大加仓次数', trigger: 'blur' }]
            },

            // 策略详情
            detailDialogVisible: false,
            strategyDetail: null
        };
    },
    
    mounted() {
        this.loadStrategySummary();
        this.loadRunningStrategies();
    },
    
    methods: {
        // 加载策略汇总
        async loadStrategySummary() {
            const result = await API.strategies.getStrategySummary();
            if (result.success) {
                this.strategySummary = result.data;
            }
        },
        
        // 处理标签页切换
        handleTabClick(tab) {
            if (tab.name === 'running') {
                this.loadRunningStrategies();
            } else {
                this.loadHistoryStrategies();
            }
        },
        
        // 加载运行中的策略
        async loadRunningStrategies() {
            const result = await API.strategies.getRunningStrategies();
            if (result.success) {
                this.runningStrategies = result.data.results || result.data;
            }
        },
        
        // 加载历史策略
        async loadHistoryStrategies() {
            const result = await API.strategies.getHistoryStrategies();
            if (result.success) {
                this.historyStrategies = result.data.results || result.data;
            }
        },
        
        // 显示创建网格策略对话框
        showCreateGridDialog() {
            this.createGridDialogVisible = true;
        },
        
        // 显示创建马丁格尔策略对话框
        showCreateMartinDialog() {
            this.createMartinDialogVisible = true;
        },
        
        // 创建网格策略
        async createGridStrategy() {
            this.$refs.gridForm.validate(async (valid) => {
                if (valid) {
                    this.createLoading = true;

                    // 转换数据类型
                    const strategyData = {
                        strategy_name: this.gridForm.strategy_name,
                        stock_code: this.gridForm.stock_code,
                        grid_min_price: parseFloat(this.gridForm.grid_min_price),
                        grid_max_price: parseFloat(this.gridForm.grid_max_price),
                        grid_count: parseInt(this.gridForm.grid_count),
                        grid_investment: parseFloat(this.gridForm.grid_investment)
                    };

                    const result = await API.strategies.createGridStrategy(strategyData);
                    this.createLoading = false;

                    if (result.success) {
                        this.showSuccess('网格策略创建成功');
                        this.createGridDialogVisible = false;
                        this.refreshData();
                        this.$refs.gridForm.resetFields();
                    } else {
                        this.showError('创建失败: ' + result.error);
                    }
                }
            });
        },
        
        // 创建马丁格尔策略
        async createMartinStrategy() {
            this.$refs.martinForm.validate(async (valid) => {
                if (valid) {
                    this.createLoading = true;

                    // 转换数据类型
                    const strategyData = {
                        strategy_name: this.martinForm.strategy_name,
                        stock_code: this.martinForm.stock_code,
                        martin_trigger_rate: parseFloat(this.martinForm.martin_trigger_rate),
                        martin_profit_target: parseFloat(this.martinForm.martin_profit_target),
                        martin_initial_amount: parseFloat(this.martinForm.martin_initial_amount),
                        martin_add_amount: parseFloat(this.martinForm.martin_add_amount),
                        martin_max_add_times: parseInt(this.martinForm.martin_max_add_times)
                    };

                    const result = await API.strategies.createMartinStrategy(strategyData);
                    this.createLoading = false;

                    if (result.success) {
                        this.showSuccess('马丁格尔策略创建成功');
                        this.createMartinDialogVisible = false;
                        this.refreshData();
                        this.$refs.martinForm.resetFields();
                    } else {
                        this.showError('创建失败: ' + result.error);
                    }
                }
            });
        },
        
        // 查看策略详情
        async viewStrategyDetail(strategyId) {
            console.log('查看策略详情被调用，策略ID:', strategyId);
            try {
                const result = await API.strategies.getStrategyDetail(strategyId);
                console.log('API返回结果:', result);
                if (result.success) {
                    this.strategyDetail = result.data;
                    this.detailDialogVisible = true;
                    console.log('策略详情对话框已打开');
                } else {
                    this.showError('获取策略详情失败: ' + result.error);
                }
            } catch (error) {
                console.log('获取策略详情出错:', error);
                this.showError('获取策略详情失败: ' + error.message);
            }
        },
        
        // 停止策略
        async stopStrategy(strategyId) {
            try {
                await this.confirmAction('确定要停止这个策略吗？停止后将自动平仓。');
                const result = await API.strategies.stopStrategy(strategyId);
                if (result.success) {
                    this.showSuccess('策略已停止');
                    this.refreshData();
                } else {
                    this.showError('停止失败: ' + result.error);
                }
            } catch (error) {
                // 用户取消操作
            }
        },
        
        // 获取价格颜色类
        getPriceColorClass(value) {
            return Utils.getPriceColorClass(value);
        },
        
        // 刷新数据
        refreshData() {
            this.loadStrategySummary();
            this.handleTabClick({ name: this.activeTab });
        },

        // 格式化数字
        formatNumber(value) {
            if (value === null || value === undefined) return '0.00';
            return parseFloat(value).toFixed(2);
        },

        // 格式化日期
        formatDate(dateString) {
            if (!dateString) return '';
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }
    }
});
