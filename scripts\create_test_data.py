#!/usr/bin/env python
"""
创建测试数据脚本
"""

import os
import sys
import django
from datetime import datetime, timedelta
from decimal import Decimal
import random

# 添加Django项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock_management.settings')
django.setup()

from accounts.models import User
from stocks.models import Stock, StockDailyData, StockMinuteData, HS300Data

def create_test_stocks():
    """创建测试股票数据"""
    test_stocks = [
        ('000001', '平安银行', 'SZ', '银行'),
        ('000002', '万科A', 'SZ', '房地产'),
        ('600000', '浦发银行', 'SH', '银行'),
        ('600036', '招商银行', 'SH', '银行'),
        ('600519', '贵州茅台', 'SH', '食品饮料'),
        ('000858', '五粮液', 'SZ', '食品饮料'),
        ('000063', '中兴通讯', 'SZ', '通信设备'),
        ('600276', '恒瑞医药', 'SH', '医药生物'),
        ('002415', '海康威视', 'SZ', '电子'),
        ('300059', '东方财富', 'SZ', '非银金融'),
    ]
    
    print("创建测试股票数据...")
    for code, name, market, industry in test_stocks:
        stock, created = Stock.objects.get_or_create(
            stock_code=code,
            defaults={
                'stock_name': name,
                'market': market,
                'industry': industry,
                'is_active': True
            }
        )
        if created:
            print(f"创建股票: {code} - {name}")

def create_test_daily_data():
    """创建测试日线数据"""
    print("创建测试日线数据...")
    stocks = Stock.objects.all()
    
    # 生成过去30天的数据
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    for stock in stocks:
        base_price = random.uniform(10, 200)  # 基础价格
        current_date = start_date
        
        while current_date <= end_date:
            # 跳过周末
            if current_date.weekday() >= 5:
                current_date += timedelta(days=1)
                continue
            
            # 生成价格数据
            change_rate = random.uniform(-0.1, 0.1)  # -10% 到 +10%
            close_price = base_price * (1 + change_rate)
            open_price = close_price * random.uniform(0.98, 1.02)
            high_price = max(open_price, close_price) * random.uniform(1.0, 1.05)
            low_price = min(open_price, close_price) * random.uniform(0.95, 1.0)
            
            volume = random.randint(1000000, 100000000)
            amount = volume * close_price
            change_amount = close_price - base_price
            
            daily_data, created = StockDailyData.objects.get_or_create(
                stock_code=stock.stock_code,
                trade_date=current_date,
                defaults={
                    'open_price': Decimal(str(round(open_price, 3))),
                    'close_price': Decimal(str(round(close_price, 3))),
                    'high_price': Decimal(str(round(high_price, 3))),
                    'low_price': Decimal(str(round(low_price, 3))),
                    'volume': volume,
                    'amount': Decimal(str(round(amount, 2))),
                    'change_rate': Decimal(str(round(change_rate * 100, 4))),
                    'change_amount': Decimal(str(round(change_amount, 3))),
                    'turnover_rate': Decimal(str(round(random.uniform(0.1, 5.0), 4)))
                }
            )
            
            base_price = close_price  # 下一天的基础价格
            current_date += timedelta(days=1)
        
        print(f"完成 {stock.stock_code} 的日线数据")

def create_test_minute_data():
    """创建测试分钟数据"""
    print("创建测试分钟数据...")
    stocks = Stock.objects.all()[:3]  # 只为前3只股票创建分钟数据
    
    # 生成今天的分钟数据
    today = datetime.now().date()
    start_time = datetime.combine(today, datetime.min.time().replace(hour=9, minute=30))
    end_time = datetime.combine(today, datetime.min.time().replace(hour=15, minute=0))
    
    for stock in stocks:
        # 获取最新的日线数据作为基础价格
        latest_daily = StockDailyData.objects.filter(
            stock_code=stock.stock_code
        ).order_by('-trade_date').first()
        
        if not latest_daily:
            continue
        
        base_price = float(latest_daily.close_price)
        current_time = start_time
        
        while current_time <= end_time:
            # 跳过中午休市时间
            if current_time.hour == 11 and current_time.minute >= 30:
                current_time = current_time.replace(hour=13, minute=0)
            if current_time.hour == 12:
                current_time = current_time.replace(hour=13, minute=0)
            
            # 生成价格数据
            change_rate = random.uniform(-0.02, 0.02)  # -2% 到 +2%
            close_price = base_price * (1 + change_rate)
            open_price = base_price
            high_price = max(open_price, close_price) * random.uniform(1.0, 1.01)
            low_price = min(open_price, close_price) * random.uniform(0.99, 1.0)
            
            volume = random.randint(10000, 1000000)
            amount = volume * close_price
            
            minute_data, created = StockMinuteData.objects.get_or_create(
                stock_code=stock.stock_code,
                datetime=current_time,
                defaults={
                    'open_price': Decimal(str(round(open_price, 3))),
                    'close_price': Decimal(str(round(close_price, 3))),
                    'high_price': Decimal(str(round(high_price, 3))),
                    'low_price': Decimal(str(round(low_price, 3))),
                    'volume': volume,
                    'amount': Decimal(str(round(amount, 2))),
                    'change_rate': Decimal(str(round(change_rate * 100, 4))),
                    'data_type': 2  # 当日实时数据
                }
            )
            
            base_price = close_price
            current_time += timedelta(minutes=1)
        
        print(f"完成 {stock.stock_code} 的分钟数据")

def create_test_hs300_data():
    """创建测试沪深300数据"""
    print("创建测试沪深300数据...")
    
    # 生成过去30天的日线数据
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    base_price = 4000.0
    
    current_date = start_date
    while current_date <= end_date:
        if current_date.weekday() >= 5:
            current_date += timedelta(days=1)
            continue
        
        change_rate = random.uniform(-0.05, 0.05)
        close_price = base_price * (1 + change_rate)
        
        hs300_data, created = HS300Data.objects.get_or_create(
            trade_date=current_date,
            data_type=1,  # 日线
            defaults={
                'close_price': Decimal(str(round(close_price, 3))),
                'change_rate': Decimal(str(round(change_rate * 100, 4)))
            }
        )
        
        base_price = close_price
        current_date += timedelta(days=1)
    
    print("完成沪深300数据")

def create_admin_user():
    """创建管理员用户"""
    print("创建管理员用户...")
    
    admin_user, created = User.objects.get_or_create(
        username='admin',
        defaults={
            'is_admin': True,
            'is_staff': True,
            'is_superuser': True,
            'real_name': '系统管理员',
            'initial_capital': Decimal('100000.00'),
            'current_capital': Decimal('100000.00')
        }
    )
    
    if created:
        admin_user.set_password('admin')
        admin_user.save()
        print("创建管理员用户: admin / admin")
    else:
        print("管理员用户已存在")

def main():
    """主函数"""
    print("开始创建测试数据...")
    
    create_admin_user()
    create_test_stocks()
    create_test_daily_data()
    create_test_minute_data()
    create_test_hs300_data()
    
    print("测试数据创建完成！")
    print("\n可以使用以下账户登录:")
    print("管理员: admin / admin")
    print("\n或者注册新用户进行测试")

if __name__ == '__main__':
    main()
