// 个人中心组件
Vue.component('profile-component', {
    template: `
        <div class="profile-component">
            <!-- 用户信息卡片 -->
            <div class="content-card">
                <div class="card-header">
                    <h3>个人信息</h3>
                </div>
                <div class="card-body">
                    <el-form :model="userProfile" label-width="100px">
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="用户名">
                                    <el-input v-model="userProfile.username" disabled></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="邮箱">
                                    <el-input v-model="userProfile.email" placeholder="请输入邮箱"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="12">
                                <el-form-item label="手机号">
                                    <el-input v-model="userProfile.phone" placeholder="请输入手机号"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="12">
                                <el-form-item label="真实姓名">
                                    <el-input v-model="userProfile.real_name" placeholder="请输入真实姓名"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-form-item>
                            <el-button type="primary" @click="updateProfile" :loading="updateLoading">更新信息</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>

            <!-- 资金信息 -->
            <div class="content-card">
                <div class="card-header">
                    <h3>资金信息</h3>
                </div>
                <div class="card-body">
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-value">{{ userProfile.total_assets | formatMoney }}</div>
                            <div class="stat-label">总资产</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value">{{ userProfile.current_capital | formatMoney }}</div>
                            <div class="stat-label">可用资金</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" :class="getPriceColorClass(userProfile.total_profit)">
                                {{ userProfile.total_profit | formatMoney }}
                            </div>
                            <div class="stat-label">总收益</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-value" :class="getPriceColorClass(userProfile.total_profit_rate)">
                                {{ userProfile.total_profit_rate | formatPercent }}
                            </div>
                            <div class="stat-label">总收益率</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 密码修改 -->
            <div class="content-card">
                <div class="card-header">
                    <h3>修改密码</h3>
                </div>
                <div class="card-body">
                    <el-form :model="passwordForm" :rules="passwordRules" ref="passwordForm" label-width="100px">
                        <el-form-item label="原密码" prop="old_password">
                            <el-input v-model="passwordForm.old_password" type="password" placeholder="请输入原密码"></el-input>
                        </el-form-item>
                        <el-form-item label="新密码" prop="new_password">
                            <el-input v-model="passwordForm.new_password" type="password" placeholder="请输入新密码"></el-input>
                        </el-form-item>
                        <el-form-item label="确认密码" prop="new_password_confirm">
                            <el-input v-model="passwordForm.new_password_confirm" type="password" placeholder="请再次输入新密码"></el-input>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" @click="changePassword" :loading="passwordLoading">修改密码</el-button>
                            <el-button @click="resetPasswordForm">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>
    `,
    
    data() {
        return {
            userProfile: {
                username: '',
                email: '',
                phone: '',
                real_name: '',
                current_capital: 0,
                total_profit: 0,
                total_profit_rate: 0,
                total_assets: 0
            },
            updateLoading: false,
            passwordForm: {
                old_password: '',
                new_password: '',
                new_password_confirm: ''
            },
            passwordRules: {
                old_password: [
                    { required: true, message: '请输入原密码', trigger: 'blur' }
                ],
                new_password: [
                    { required: true, message: '请输入新密码', trigger: 'blur' },
                    { min: 6, message: '密码长度至少 6 个字符', trigger: 'blur' }
                ],
                new_password_confirm: [
                    { required: true, message: '请再次输入新密码', trigger: 'blur' },
                    { validator: this.validatePasswordConfirm, trigger: 'blur' }
                ]
            },
            passwordLoading: false
        };
    },
    
    mounted() {
        this.loadUserProfile();
    },

    watch: {
        // 监听父组件的登录状态变化
        '$parent.isLoggedIn': {
            handler(newVal) {
                if (newVal) {
                    this.loadUserProfile();
                } else {
                    // 用户退出登录，清空数据
                    this.userProfile = {
                        username: '',
                        email: '',
                        real_name: '',
                        current_capital: 0,
                        available_capital: 0,
                        total_assets: 0,
                        profit_rate: 0
                    };
                }
            }
        }
    },
    
    methods: {
        // 加载用户资料
        async loadUserProfile() {
            const result = await API.auth.getProfile();
            if (result.success) {
                this.userProfile = result.data;
            }
        },
        
        // 更新用户信息
        async updateProfile() {
            this.updateLoading = true;
            // 这里应该调用更新用户信息的API
            // 由于当前API设计中没有单独的更新接口，这里模拟
            setTimeout(() => {
                this.updateLoading = false;
                this.showSuccess('用户信息更新成功');
            }, 1000);
        },
        
        // 修改密码
        async changePassword() {
            this.$refs.passwordForm.validate(async (valid) => {
                if (valid) {
                    this.passwordLoading = true;
                    const result = await API.auth.changePassword(
                        this.passwordForm.old_password,
                        this.passwordForm.new_password,
                        this.passwordForm.new_password_confirm
                    );
                    this.passwordLoading = false;
                    
                    if (result.success) {
                        this.showSuccess('密码修改成功');
                        this.resetPasswordForm();
                    } else {
                        this.showError('密码修改失败: ' + result.error);
                    }
                }
            });
        },
        
        // 验证确认密码
        validatePasswordConfirm(rule, value, callback) {
            if (value !== this.passwordForm.new_password) {
                callback(new Error('两次输入的密码不一致'));
            } else {
                callback();
            }
        },
        
        // 重置密码表单
        resetPasswordForm() {
            this.$refs.passwordForm.resetFields();
        },
        
        // 退出登录
        async logout() {
            try {
                await this.confirmAction('确定要退出登录吗？');

                // 直接刷新页面，最简单有效
                window.location.reload();

            } catch (error) {
                // 用户取消操作
            }
        },
        
        // 获取价格颜色类
        getPriceColorClass(value) {
            return Utils.getPriceColorClass(value);
        },
        
        // 刷新数据
        refreshData() {
            this.loadUserProfile();
        }
    }
});
