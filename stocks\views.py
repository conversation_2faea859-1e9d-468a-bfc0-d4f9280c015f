from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView, CreateAPIView, DestroyAPIView
from django.shortcuts import get_object_or_404
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from .models import Stock, StockDailyData, StockMinuteData, HS300Data, UserFavorite
from .serializers import (
    StockSerializer, StockDetailSerializer, StockRankingSerializer,
    UserFavoriteSerializer, StockChartDataSerializer, HS300DataSerializer
)
from .services import StockDataService


class StockRankingView(APIView):
    """股票涨幅排行"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request):
        period = request.query_params.get('period', '1Y')  # 1Y, 1M
        limit = int(request.query_params.get('limit', 20))
        
        service = StockDataService()
        ranking_data = service.get_stock_ranking(period=period, limit=limit)
        
        serializer = StockRankingSerializer(ranking_data, many=True)
        return Response(serializer.data)


class StockSearchView(APIView):
    """股票搜索"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request):
        keyword = request.query_params.get('keyword', '')
        if not keyword:
            return Response({'error': '请提供搜索关键词'}, status=status.HTTP_400_BAD_REQUEST)
        
        service = StockDataService()
        stocks = service.search_stock(keyword)
        
        serializer = StockDetailSerializer(stocks, many=True, context={'request': request})
        return Response(serializer.data)


class StockDetailView(APIView):
    """股票详情"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, stock_code):
        try:
            stock = Stock.objects.get(stock_code=stock_code)
            serializer = StockDetailSerializer(stock, context={'request': request})
            return Response(serializer.data)
        except Stock.DoesNotExist:
            return Response({'error': '股票不存在'}, status=status.HTTP_404_NOT_FOUND)


class StockChartDataView(APIView):
    """股票图表数据"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, stock_code):
        period = request.query_params.get('period', '1D')  # 1min, 1D, 1W, 1M, 3M, 6M
        
        service = StockDataService()
        chart_data = service.get_stock_chart_data(stock_code, period)
        
        # 转换数据格式
        data_list = []
        for item in chart_data:
            if hasattr(item, 'datetime'):  # 分钟数据
                data_list.append({
                    'datetime': item.datetime,
                    'open_price': item.open_price,
                    'close_price': item.close_price,
                    'high_price': item.high_price,
                    'low_price': item.low_price,
                    'volume': item.volume,
                    'change_rate': item.change_rate,
                })
            else:  # 日线数据
                data_list.append({
                    'datetime': timezone.datetime.combine(item.trade_date, timezone.datetime.min.time()),
                    'open_price': item.open_price,
                    'close_price': item.close_price,
                    'high_price': item.high_price,
                    'low_price': item.low_price,
                    'volume': item.volume,
                    'change_rate': item.change_rate,
                })
        
        serializer = StockChartDataSerializer(data_list, many=True)
        return Response(serializer.data)


class HS300ChartDataView(APIView):
    """沪深300图表数据"""
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        try:
            # 获取查询参数
            period = request.GET.get('period', '1D')

            # 根据周期获取对应时间范围的数据
            from datetime import datetime, timedelta
            end_date = datetime.now().date()

            if period == '1D':
                start_date = end_date - timedelta(days=1)
            elif period == '1W':
                start_date = end_date - timedelta(days=7)
            elif period == '1M':
                start_date = end_date - timedelta(days=30)
            elif period == '3M':
                start_date = end_date - timedelta(days=90)
            elif period == '6M':
                start_date = end_date - timedelta(days=180)
            else:
                start_date = end_date - timedelta(days=30)

            # 获取指定时间范围的沪深300日线数据
            hs300_data = HS300Data.objects.filter(
                data_type=1,
                trade_date__gte=start_date,
                trade_date__lte=end_date
            ).order_by('trade_date')

            data_list = []
            for item in hs300_data:
                data_list.append({
                    'datetime': item.trade_date.strftime('%Y-%m-%d'),
                    'close_price': float(item.close_price),
                    'change_rate': float(item.change_rate) if item.change_rate else 0.0,
                })

            print(f"沪深300数据API: period={period}, 返回{len(data_list)}条数据, 时间范围:{start_date}到{end_date}")
            return Response(data_list)

        except Exception as e:
            import traceback
            print(f"沪深300数据获取失败: {e}")
            print(traceback.format_exc())
            return Response([])


class HS300TestView(APIView):
    """沪深300测试视图"""
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        try:
            # 简单的数据查询测试
            total_count = HS300Data.objects.count()
            daily_count = HS300Data.objects.filter(data_type=1).count()

            # 获取最新的5条数据
            latest_data = HS300Data.objects.filter(data_type=1).order_by('-trade_date')[:5]

            data_list = []
            for item in latest_data:
                data_list.append({
                    'date': item.trade_date.strftime('%Y-%m-%d'),
                    'price': float(item.close_price),
                    'change': float(item.change_rate) if item.change_rate else 0.0,
                })

            return Response({
                'total_count': total_count,
                'daily_count': daily_count,
                'latest_data': data_list,
                'message': 'HS300 test successful'
            })

        except Exception as e:
            import traceback
            return Response({
                'error': str(e),
                'traceback': traceback.format_exc()
            })


class HS300TestView(APIView):
    """沪深300测试视图"""
    permission_classes = [permissions.AllowAny]

    def get(self, request):
        try:
            # 简单的数据查询测试
            total_count = HS300Data.objects.count()
            daily_count = HS300Data.objects.filter(data_type=1).count()

            # 获取最新的5条数据
            latest_data = HS300Data.objects.filter(data_type=1).order_by('-trade_date')[:5]

            data_list = []
            for item in latest_data:
                data_list.append({
                    'date': item.trade_date.strftime('%Y-%m-%d'),
                    'price': float(item.close_price),
                    'change': float(item.change_rate) if item.change_rate else 0.0,
                })

            return Response({
                'total_count': total_count,
                'daily_count': daily_count,
                'latest_data': data_list,
                'message': 'HS300 test successful'
            })

        except Exception as e:
            import traceback
            return Response({
                'error': str(e),
                'traceback': traceback.format_exc()
            })


class StockPeriodChangeView(APIView):
    """股票期间涨跌幅"""
    permission_classes = [permissions.AllowAny]
    
    def get(self, request, stock_code):
        periods = ['1min', '1D', '1W', '1M', '3M', '6M']
        service = StockDataService()
        
        result = {}
        for period in periods:
            change_data = service.calculate_period_change(stock_code, period)
            if change_data:
                result[period] = change_data
            else:
                result[period] = {'change_rate': '--', 'change_amount': '--'}
        
        return Response(result)


class UserFavoriteListView(ListAPIView):
    """用户收藏列表"""
    serializer_class = UserFavoriteSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return UserFavorite.objects.filter(user=self.request.user).order_by('-created_at')


@method_decorator(csrf_exempt, name='dispatch')
class AddFavoriteView(APIView):
    """添加收藏"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        stock_code = request.data.get('stock_code')
        if not stock_code:
            return Response({'error': '请提供股票代码'}, status=status.HTTP_400_BAD_REQUEST)
        
        # 检查股票是否存在
        try:
            stock = Stock.objects.get(stock_code=stock_code)
        except Stock.DoesNotExist:
            return Response({'error': '股票不存在'}, status=status.HTTP_404_NOT_FOUND)
        
        # 检查是否已收藏
        if UserFavorite.objects.filter(user=request.user, stock_code=stock_code).exists():
            return Response({'error': '已经收藏过该股票'}, status=status.HTTP_400_BAD_REQUEST)
        
        # 获取当前价格
        service = StockDataService()
        current_price = service.get_current_price(stock_code)
        
        # 创建收藏
        favorite = UserFavorite.objects.create(
            user=request.user,
            stock_code=stock_code,
            favorite_date=timezone.now().date(),
            favorite_price=current_price
        )
        
        serializer = UserFavoriteSerializer(favorite)
        return Response(serializer.data, status=status.HTTP_201_CREATED)


@method_decorator(csrf_exempt, name='dispatch')
class RemoveFavoriteView(APIView):
    """取消收藏"""
    permission_classes = [permissions.IsAuthenticated]
    
    def delete(self, request, stock_code):
        try:
            favorite = UserFavorite.objects.get(user=request.user, stock_code=stock_code)
            favorite.delete()
            return Response({'message': '取消收藏成功'}, status=status.HTTP_200_OK)
        except UserFavorite.DoesNotExist:
            return Response({'error': '未收藏该股票'}, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def get_current_price(request, stock_code):
    """获取股票当前价格"""
    service = StockDataService()
    current_price = service.get_current_price(stock_code)
    
    if current_price is not None:
        return Response({'stock_code': stock_code, 'current_price': current_price})
    else:
        return Response({'error': '无法获取当前价格'}, status=status.HTTP_404_NOT_FOUND)
