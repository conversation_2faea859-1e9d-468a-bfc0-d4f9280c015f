#!/usr/bin/env python
"""
MySQL版本的股票数据爬取系统
包含日线数据、历史分钟数据、当日实时分钟数据
支持收盘价走势图绘制
"""

import os
import sys
import django
from datetime import datetime, timedelta, date
import pandas as pd
import akshare as ak
import tushare as ts
import time
import logging
from decimal import Decimal
import pymysql
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

# 添加Django项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock_management.settings')
django.setup()

from stocks.models import Stock, StockDailyData, StockMinuteData, HS300Data

# 配置日志
logging.basicConfig(
    level=logging.INFO, 
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mysql_stock_data_fetch.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Tushare配置
TUSHARE_TOKEN = '4663a4811b61b7ae71fbee8e0e84e2600c8d03530ce45ace9f58c3cd'
ts.set_token(TUSHARE_TOKEN)
pro = ts.pro_api()

# MySQL数据库配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': '123456',
    'database': 'stock_management',
    'charset': 'utf8mb4'
}

class MySQLStockDataFetcher:
    def __init__(self):
        self.sleep_time = 0.3  # 请求间隔，避免被限制
        self.batch_size = 50   # 批量处理大小
        
    def test_mysql_connection(self):
        """测试MySQL连接"""
        try:
            connection = pymysql.connect(**MYSQL_CONFIG)
            cursor = connection.cursor()
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            logger.info(f"MySQL连接成功，版本: {version[0]}")
            cursor.close()
            connection.close()
            return True
        except Exception as e:
            logger.error(f"MySQL连接失败: {e}")
            return False
    
    def get_stock_list(self):
        """获取A股股票列表"""
        logger.info("开始获取A股股票列表...")
        
        try:
            # 使用AKShare获取股票基本信息
            stock_info = ak.stock_info_a_code_name()
            logger.info(f"获取到 {len(stock_info)} 只股票")
            
            # 批量创建股票记录
            stocks_to_create = []
            for _, row in stock_info.iterrows():
                code = row['code']
                name = row['name']
                
                # 判断市场
                if code.startswith('6'):
                    market = 'SH'
                elif code.startswith(('0', '3')):
                    market = 'SZ'
                else:
                    continue
                
                # 检查是否已存在
                if not Stock.objects.filter(stock_code=code).exists():
                    stocks_to_create.append(Stock(
                        stock_code=code,
                        stock_name=name,
                        market=market,
                        is_active=True
                    ))
            
            # 批量创建
            if stocks_to_create:
                Stock.objects.bulk_create(stocks_to_create, batch_size=self.batch_size)
                logger.info(f"新增 {len(stocks_to_create)} 只股票")
            
            return Stock.objects.filter(is_active=True).count()
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return 0
    
    def fetch_daily_data(self, days_back=365):
        """获取日线数据（历史数据，不包含当日）"""
        logger.info(f"开始获取过去 {days_back} 天的日线数据...")
        
        # 计算日期范围（不包含今天）
        end_date = (datetime.now() - timedelta(days=1)).strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y%m%d')
        
        logger.info(f"数据范围: {start_date} 到 {end_date}")
        
        # 获取所有活跃股票
        stocks = Stock.objects.filter(is_active=True)
        total_stocks = stocks.count()
        
        success_count = 0
        error_count = 0
        
        for i, stock in enumerate(stocks, 1):
            try:
                logger.info(f"[{i}/{total_stocks}] 获取 {stock.stock_code} - {stock.stock_name} 的日线数据")
                
                # 使用AKShare获取历史日线数据
                df = ak.stock_zh_a_hist(
                    symbol=stock.stock_code,
                    period="daily",
                    start_date=start_date,
                    end_date=end_date,
                    adjust=""
                )
                
                if df.empty:
                    logger.warning(f"{stock.stock_code} 无数据")
                    continue
                
                # 批量创建日线数据
                daily_data_to_create = []
                for _, row in df.iterrows():
                    trade_date = pd.to_datetime(row['日期']).date()
                    
                    # 检查是否已存在
                    if StockDailyData.objects.filter(
                        stock_code=stock.stock_code, 
                        trade_date=trade_date
                    ).exists():
                        continue
                    
                    # 计算涨跌幅和涨跌额
                    change_rate = row.get('涨跌幅', 0)
                    change_amount = row.get('涨跌额', 0)
                    
                    daily_data_to_create.append(StockDailyData(
                        stock_code=stock.stock_code,
                        trade_date=trade_date,
                        open_price=Decimal(str(row['开盘'])),
                        close_price=Decimal(str(row['收盘'])),
                        high_price=Decimal(str(row['最高'])),
                        low_price=Decimal(str(row['最低'])),
                        volume=int(row['成交量']) if pd.notna(row['成交量']) else 0,
                        amount=Decimal(str(row['成交额'])) if pd.notna(row['成交额']) else Decimal('0'),
                        change_rate=Decimal(str(change_rate)) if pd.notna(change_rate) else Decimal('0'),
                        change_amount=Decimal(str(change_amount)) if pd.notna(change_amount) else Decimal('0'),
                        turnover_rate=Decimal(str(row.get('换手率', 0))) if pd.notna(row.get('换手率', 0)) else Decimal('0')
                    ))
                
                # 批量插入
                if daily_data_to_create:
                    StockDailyData.objects.bulk_create(daily_data_to_create, batch_size=self.batch_size)
                    logger.info(f"{stock.stock_code} 新增 {len(daily_data_to_create)} 条日线数据")
                
                success_count += 1
                time.sleep(self.sleep_time)
                
            except Exception as e:
                error_count += 1
                logger.error(f"获取 {stock.stock_code} 日线数据失败: {e}")
                continue
        
        logger.info(f"日线数据获取完成: 成功 {success_count}, 失败 {error_count}")
        return success_count
    
    def plot_stock_close_price(self, stock_code, days=30):
        """绘制股票收盘价走势图"""
        try:
            # 获取股票信息
            stock = Stock.objects.get(stock_code=stock_code)
            
            # 获取最近N天的日线数据
            end_date = datetime.now().date()
            start_date = end_date - timedelta(days=days)
            
            daily_data = StockDailyData.objects.filter(
                stock_code=stock_code,
                trade_date__gte=start_date,
                trade_date__lte=end_date
            ).order_by('trade_date')
            
            if not daily_data.exists():
                logger.warning(f"股票 {stock_code} 没有数据")
                return False
            
            # 准备数据
            dates = [data.trade_date for data in daily_data]
            close_prices = [float(data.close_price) for data in daily_data]
            
            # 创建图表
            plt.figure(figsize=(12, 6))
            plt.plot(dates, close_prices, linewidth=2, color='#E74C3C', marker='o', markersize=3)
            
            # 设置标题和标签
            plt.title(f'{stock.stock_name} ({stock_code}) 收盘价走势图', fontsize=16, fontweight='bold')
            plt.xlabel('日期', fontsize=12)
            plt.ylabel('收盘价 (元)', fontsize=12)
            
            # 格式化x轴日期
            plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
            plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(dates)//10)))
            plt.xticks(rotation=45)
            
            # 添加网格
            plt.grid(True, alpha=0.3)
            
            # 调整布局
            plt.tight_layout()
            
            # 保存图片
            filename = f'{stock_code}_{stock.stock_name}_收盘价走势图.png'
            plt.savefig(filename, dpi=300, bbox_inches='tight')
            logger.info(f"收盘价走势图已保存: {filename}")
            
            # 显示图表
            plt.show()
            
            return True
            
        except Stock.DoesNotExist:
            logger.error(f"股票 {stock_code} 不存在")
            return False
        except Exception as e:
            logger.error(f"绘制股票 {stock_code} 走势图失败: {e}")
            return False

def main():
    """主函数"""
    fetcher = MySQLStockDataFetcher()
    
    print("=" * 60)
    print("MySQL版本股票数据爬取系统")
    print("=" * 60)
    print("1. 测试MySQL连接")
    print("2. 获取股票列表")
    print("3. 获取日线数据")
    print("4. 绘制股票收盘价走势图")
    print("5. 执行完整数据爬取")
    print("0. 退出")
    print("=" * 60)
    
    while True:
        choice = input("请选择要执行的操作 (0-5): ").strip()
        
        if choice == '0':
            print("退出程序")
            break
        elif choice == '1':
            success = fetcher.test_mysql_connection()
            print("MySQL连接" + ("成功" if success else "失败"))
        elif choice == '2':
            count = fetcher.get_stock_list()
            print(f"股票列表更新完成，共 {count} 只股票")
        elif choice == '3':
            count = fetcher.fetch_daily_data()
            print(f"日线数据获取完成，成功处理 {count} 只股票")
        elif choice == '4':
            stock_code = input("请输入股票代码 (如: 000001): ").strip()
            days = input("请输入天数 (默认30天): ").strip()
            days = int(days) if days.isdigit() else 30
            success = fetcher.plot_stock_close_price(stock_code, days)
            print("走势图绘制" + ("成功" if success else "失败"))
        elif choice == '5':
            print("开始执行完整数据爬取...")
            print("\n步骤1: 测试MySQL连接")
            if not fetcher.test_mysql_connection():
                print("❌ MySQL连接失败，请检查配置")
                continue
            print("✓ MySQL连接成功")
            
            print("\n步骤2: 获取股票列表")
            stock_count = fetcher.get_stock_list()
            print(f"✓ 股票列表: {stock_count} 只")
            
            print("\n步骤3: 获取日线数据")
            daily_count = fetcher.fetch_daily_data()
            print(f"✓ 日线数据: {daily_count} 只股票")
            
            print("\n" + "=" * 60)
            print("完整数据爬取完成！")
            print("=" * 60)
            break
        else:
            print("无效选择，请重新输入")

if __name__ == '__main__':
    main()
