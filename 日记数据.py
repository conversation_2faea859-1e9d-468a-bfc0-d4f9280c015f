import tushare as ts
import pandas as pd
from datetime import datetime, timed<PERSON>ta
from sqlalchemy import create_engine
import time
import os

# 1. 参数 ------------------------------------------------------------------
TOKEN      = '4663a4811b61b7ae71fbee8e0e84e2600c8d03530ce45ace9f58c3cd'  # 换成你的
START_DATE = '20200101'
END_DATE   = '20250723'
DB_PATH    = 'a_stock_close.db'          # 生成的 SQLite 文件名
TABLE_NAME = 'daily_close'               # 表名

# 2. 连接 Tushare ----------------------------------------------------------
ts.set_token(TOKEN)
pro = ts.pro_api()

# 3. 连接 SQLite -----------------------------------------------------------
engine = create_engine(f'sqlite:///{DB_PATH}', echo=False)

# 4. 建表（如已存在则跳过）
create_sql = f"""
CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    ts_code     TEXT,
    trade_date  TEXT,
    close       REAL,
    PRIMARY KEY (ts_code, trade_date)
);
"""
with engine.connect() as conn:
    conn.exec_driver_sql(create_sql)

# 5. 限速参数 --------------------------------------------------------------
LIMIT_PER_REQ = 6000
REQ_PER_MIN   = 500
SLEEP_SEC     = 60 / REQ_PER_MIN + 0.1

# 6. 分批拉取并写入 --------------------------------------------------------
def fetch_and_save(date_str: str):
    """拉取某一天的 close 数据并写入数据库"""
    offset = 0
    while True:
        df = pro.daily(trade_date=date_str,
                       fields='ts_code,trade_date,close',
                       limit=LIMIT_PER_REQ,
                       offset=offset)
        if df.empty:
            break
        df.to_sql(TABLE_NAME, con=engine,
                  if_exists='append', index=False, method='multi')
        offset += LIMIT_PER_REQ
        time.sleep(SLEEP_SEC)

# 7. 主循环：按自然日遍历 --------------------------------------------------
start_dt = datetime.strptime(START_DATE, '%Y%m%d')
end_dt   = datetime.strptime(END_DATE,   '%Y%m%d')

day = start_dt
while day <= end_dt:
    date_str = day.strftime('%Y%m%d')
    print(f'--> {date_str}')
    fetch_and_save(date_str)
    day += timedelta(days=1)

print('All done! 数据已写入', os.path.abspath(DB_PATH))