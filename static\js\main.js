// 主Vue应用
new Vue({
    el: '#app',
    data() {
        return {
            // 用户状态
            isLoggedIn: false,
            currentUser: null,
            
            // 界面状态
            activeMenu: 'home',
            searchKeyword: '',
            
            // 登录相关
            loginDialogVisible: false,
            loginLoading: false,
            loginForm: {
                username: '',
                password: ''
            },
            loginRules: {
                username: [
                    { required: true, message: '请输入用户名', trigger: 'blur' }
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: 'blur' }
                ]
            },
            
            // 注册相关
            registerDialogVisible: false,
            registerLoading: false,
            registerForm: {
                username: '',
                password: '',
                password_confirm: '',
                email: '',
                real_name: ''
            },
            registerRules: {
                username: [
                    { required: true, message: '请输入用户名', trigger: 'blur' },
                    { min: 3, max: 20, message: '用户名长度在 3 到 20 个字符', trigger: 'blur' }
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: 'blur' },
                    { min: 6, message: '密码长度至少 6 个字符', trigger: 'blur' }
                ],
                password_confirm: [
                    { required: true, message: '请再次输入密码', trigger: 'blur' },
                    { validator: this.validatePasswordConfirm, trigger: 'blur' }
                ]
            },

            // 切换账户相关
            switchAccountDialogVisible: false,
            switchAccountLoading: false,
            switchAccountForm: {
                username: '',
                password: ''
            },
            switchAccountRules: {
                username: [
                    { required: true, message: '请输入用户名', trigger: 'blur' }
                ],
                password: [
                    { required: true, message: '请输入密码', trigger: 'blur' }
                ]
            }
        };
    },
    
    mounted() {
        this.checkLoginStatus();
    },
    
    methods: {
        // 检查登录状态
        async checkLoginStatus() {
            console.log('检查登录状态...');
            const result = await API.auth.getProfile();
            console.log('登录状态检查结果:', result);

            if (result.success) {
                console.log('用户已登录:', result.data);
                this.isLoggedIn = true;
                this.currentUser = result.data;
            } else {
                console.log('用户未登录，显示登录对话框');
                this.isLoggedIn = false;
                this.currentUser = null;
                // 立即显示登录对话框
                this.loginDialogVisible = true;
            }
        },
        
        // 显示登录对话框
        showLoginDialog() {
            this.loginDialogVisible = true;
            this.registerDialogVisible = false;
        },
        
        // 显示注册对话框
        showRegisterDialog() {
            this.registerDialogVisible = true;
            this.loginDialogVisible = false;
        },
        
        // 处理登录
        async handleLogin() {
            this.$refs.loginForm.validate(async (valid) => {
                if (valid) {
                    this.loginLoading = true;
                    const result = await API.auth.login(this.loginForm.username, this.loginForm.password);
                    this.loginLoading = false;
                    
                    if (result.success) {
                        this.isLoggedIn = true;
                        this.currentUser = result.data.user;
                        this.loginDialogVisible = false;
                        this.$message.success('登录成功');
                        
                        // 重置表单
                        this.loginForm = { username: '', password: '' };
                        this.$refs.loginForm.resetFields();
                    } else {
                        this.$message.error(result.error);
                    }
                }
            });
        },
        
        // 处理注册
        async handleRegister() {
            this.$refs.registerForm.validate(async (valid) => {
                if (valid) {
                    this.registerLoading = true;
                    const result = await API.auth.register(this.registerForm);
                    this.registerLoading = false;
                    
                    if (result.success) {
                        this.$message.success('注册成功，请登录');
                        this.registerDialogVisible = false;
                        this.showLoginDialog();
                        
                        // 重置表单
                        this.registerForm = {
                            username: '',
                            password: '',
                            password_confirm: '',
                            email: '',
                            real_name: ''
                        };
                        this.$refs.registerForm.resetFields();
                    } else {
                        this.$message.error(result.error);
                    }
                }
            });
        },
        
        // 验证确认密码
        validatePasswordConfirm(rule, value, callback) {
            if (value !== this.registerForm.password) {
                callback(new Error('两次输入的密码不一致'));
            } else {
                callback();
            }
        },
        
        // 处理用户下拉菜单命令
        handleUserCommand(command) {
            if (command === 'logout') {
                this.logout();
            } else if (command === 'profile') {
                this.activeMenu = 'profile';
            } else if (command === 'switch') {
                this.showSwitchAccountDialog();
            }
        },

        // 处理登出
        logout() {
            this.$confirm('确定要退出登录吗？', '退出确认', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(async () => {
                // 用户确认退出
                console.log('用户确认退出，开始退出流程');

                try {
                    // 先调用后端API清除session
                    await API.auth.logout();
                    console.log('后端session已清除');
                } catch (error) {
                    console.log('后端退出API调用失败，但继续本地清理:', error);
                }

                // 清理本地状态
                this.isLoggedIn = false;
                this.currentUser = null;
                this.activeMenu = 'home';

                // 显示退出消息
                this.$message({
                    message: '已退出登录',
                    type: 'success',
                    duration: 1000
                });

                // 延迟一点时间后刷新页面
                setTimeout(() => {
                    console.log('刷新页面');
                    window.location.reload();
                }, 1000);

            }).catch(() => {
                // 用户取消退出
                console.log('用户取消退出');
            });
        },

        // 强制退出（用于测试）
        forceLogout() {
            console.log('强制退出被调用');
            window.location.reload();
        },

        // 显示切换账户对话框
        showSwitchAccountDialog() {
            this.switchAccountDialogVisible = true;
            this.switchAccountForm.username = '';
            this.switchAccountForm.password = '';
        },

        // 切换账户
        async switchAccount() {
            this.$refs.switchAccountForm.validate(async (valid) => {
                if (valid) {
                    this.switchAccountLoading = true;

                    try {
                        // 先登出当前账户
                        await API.auth.logout();

                        // 登录新账户
                        const result = await API.auth.login(
                            this.switchAccountForm.username,
                            this.switchAccountForm.password
                        );

                        this.switchAccountLoading = false;

                        if (result.success) {
                            this.isLoggedIn = true;
                            this.currentUser = result.data.user;
                            this.switchAccountDialogVisible = false;
                            this.$message.success('账户切换成功');
                            this.$refs.switchAccountForm.resetFields();
                        } else {
                            this.$message.error('切换失败: ' + result.error);
                        }
                    } catch (error) {
                        this.switchAccountLoading = false;
                        this.$message.error('切换失败: ' + error.message);
                    }
                }
            });
        },

        // 处理菜单选择
        handleMenuSelect(key) {
            // 检查是否需要登录
            const requiresLogin = ['favorites', 'trading', 'strategies', 'profile', 'admin'];

            if (requiresLogin.includes(key) && !this.isLoggedIn) {
                this.$message.warning('请先登录');
                this.showLoginDialog();
                return;
            }

            this.activeMenu = key;

            // 刷新对应组件数据
            this.$nextTick(() => {
                if (this.$refs[key + 'Component'] && this.$refs[key + 'Component'].refreshData) {
                    this.$refs[key + 'Component'].refreshData();
                }
            });
        },
        
        // 搜索股票
        async searchStock() {
            if (!this.searchKeyword.trim()) {
                this.$message.warning('请输入搜索关键词');
                return;
            }
            
            // 切换到主界面并执行搜索
            this.activeMenu = 'home';
            this.$nextTick(() => {
                if (this.$refs.homeComponent) {
                    this.$refs.homeComponent.searchStock(this.searchKeyword);
                }
            });
        }
    },
    
    // 全局错误处理
    errorCaptured(err, vm, info) {
        console.error('Vue错误:', err, info);
        this.$message.error('系统错误，请刷新页面重试');
        return false;
    }
});

// 全局配置
Vue.config.productionTip = false;

// 全局过滤器
Vue.filter('formatNumber', Utils.formatNumber);
Vue.filter('formatPercent', Utils.formatPercent);
Vue.filter('formatMoney', Utils.formatMoney);
Vue.filter('formatDateTime', Utils.formatDateTime);
Vue.filter('formatDate', Utils.formatDate);

// 全局混入
Vue.mixin({
    methods: {
        // 显示加载状态
        showLoading(message = '加载中...') {
            return this.$loading({
                lock: true,
                text: message,
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
        },
        
        // 确认对话框
        confirmAction(message, title = '提示') {
            return this.$confirm(message, title, {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            });
        },
        
        // 成功消息
        showSuccess(message) {
            this.$message.success(message);
        },
        
        // 错误消息
        showError(message) {
            this.$message.error(message);
        },
        
        // 警告消息
        showWarning(message) {
            this.$message.warning(message);
        }
    }
});
