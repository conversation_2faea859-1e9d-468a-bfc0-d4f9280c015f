from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from .models import User


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    list_display = ('username', 'email', 'real_name', 'phone', 'is_admin', 'current_capital', 'total_profit', 'created_at')
    list_filter = ('is_admin', 'is_staff', 'is_active', 'created_at')
    search_fields = ('username', 'email', 'real_name', 'phone')
    ordering = ('-created_at',)
    
    fieldsets = BaseUserAdmin.fieldsets + (
        ('扩展信息', {
            'fields': ('phone', 'real_name', 'is_admin')
        }),
        ('交易信息', {
            'fields': ('initial_capital', 'current_capital', 'total_profit', 'total_profit_rate')
        }),
    )
    
    add_fieldsets = BaseUserAdmin.add_fieldsets + (
        ('扩展信息', {
            'fields': ('phone', 'real_name', 'is_admin', 'initial_capital')
        }),
    )
