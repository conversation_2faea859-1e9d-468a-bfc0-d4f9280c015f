from rest_framework import status, permissions
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework.generics import ListAPIView, RetrieveAPIView
from django.shortcuts import get_object_or_404
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from .models import TradingStrategy, StrategyExecution
from .serializers import (
    CreateGridStrategySerializer, CreateMartinStrategySerializer,
    TradingStrategySerializer, StrategyDetailSerializer, StrategyExecutionSerializer
)
from .services import StrategyService


@method_decorator(csrf_exempt, name='dispatch')
class CreateGridStrategyView(APIView):
    """创建网格交易策略"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = CreateGridStrategySerializer(data=request.data)
        if serializer.is_valid():
            service = StrategyService()
            result = service.create_grid_strategy(
                user=request.user,
                strategy_name=serializer.validated_data['strategy_name'],
                stock_code=serializer.validated_data['stock_code'],
                min_price=serializer.validated_data['grid_min_price'],
                max_price=serializer.validated_data['grid_max_price'],
                grid_count=serializer.validated_data['grid_count'],
                investment=serializer.validated_data['grid_investment']
            )
            
            if result['success']:
                strategy_serializer = TradingStrategySerializer(result['strategy'])
                return Response({
                    'message': '网格策略创建成功',
                    'strategy': strategy_serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({'error': result['message']}, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(csrf_exempt, name='dispatch')
class CreateMartinStrategyView(APIView):
    """创建马丁格尔策略"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request):
        serializer = CreateMartinStrategySerializer(data=request.data)
        if serializer.is_valid():
            service = StrategyService()
            result = service.create_martin_strategy(
                user=request.user,
                strategy_name=serializer.validated_data['strategy_name'],
                stock_code=serializer.validated_data['stock_code'],
                trigger_rate=serializer.validated_data['martin_trigger_rate'],
                profit_target=serializer.validated_data['martin_profit_target'],
                initial_amount=serializer.validated_data['martin_initial_amount'],
                add_amount=serializer.validated_data['martin_add_amount'],
                max_add_times=serializer.validated_data['martin_max_add_times']
            )
            
            if result['success']:
                strategy_serializer = TradingStrategySerializer(result['strategy'])
                return Response({
                    'message': '马丁格尔策略创建成功',
                    'strategy': strategy_serializer.data
                }, status=status.HTTP_201_CREATED)
            else:
                return Response({'error': result['message']}, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class RunningStrategiesView(ListAPIView):
    """运行中的策略列表"""
    serializer_class = TradingStrategySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TradingStrategy.objects.filter(
            user=self.request.user,
            status=1  # 运行中
        ).order_by('-created_at')


class HistoryStrategiesView(ListAPIView):
    """历史策略列表"""
    serializer_class = TradingStrategySerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TradingStrategy.objects.filter(
            user=self.request.user,
            status__in=[2, 3]  # 已停止、已完成
        ).order_by('-end_time')


class StrategyDetailView(RetrieveAPIView):
    """策略详情"""
    serializer_class = StrategyDetailSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        return TradingStrategy.objects.filter(user=self.request.user)


@method_decorator(csrf_exempt, name='dispatch')
class StopStrategyView(APIView):
    """停止策略"""
    permission_classes = [permissions.IsAuthenticated]
    
    def post(self, request, strategy_id):
        service = StrategyService()
        result = service.stop_strategy(strategy_id, request.user)
        
        if result['success']:
            return Response({'message': result['message']}, status=status.HTTP_200_OK)
        else:
            return Response({'error': result['message']}, status=status.HTTP_400_BAD_REQUEST)


class StrategyExecutionsView(ListAPIView):
    """策略执行记录"""
    serializer_class = StrategyExecutionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        strategy_id = self.kwargs.get('strategy_id')
        try:
            strategy = TradingStrategy.objects.get(id=strategy_id, user=self.request.user)
            return StrategyExecution.objects.filter(strategy=strategy).order_by('-execution_time')
        except TradingStrategy.DoesNotExist:
            return StrategyExecution.objects.none()


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def strategy_summary(request):
    """策略汇总统计"""
    user = request.user
    
    # 统计各类策略数量
    total_strategies = TradingStrategy.objects.filter(user=user).count()
    running_strategies = TradingStrategy.objects.filter(user=user, status=1).count()
    completed_strategies = TradingStrategy.objects.filter(user=user, status=3).count()
    stopped_strategies = TradingStrategy.objects.filter(user=user, status=2).count()
    
    # 统计总收益
    strategies = TradingStrategy.objects.filter(user=user)
    total_investment = sum(s.total_investment for s in strategies)
    total_realized_profit = sum(s.realized_profit for s in strategies)
    
    # 计算总收益率
    total_profit_rate = 0
    if total_investment > 0:
        total_profit_rate = (total_realized_profit / total_investment) * 100
    
    # 按策略类型统计
    grid_strategies = strategies.filter(strategy_type=1).count()
    martin_strategies = strategies.filter(strategy_type=2).count()
    
    return Response({
        'total_strategies': total_strategies,
        'running_strategies': running_strategies,
        'completed_strategies': completed_strategies,
        'stopped_strategies': stopped_strategies,
        'grid_strategies': grid_strategies,
        'martin_strategies': martin_strategies,
        'total_investment': total_investment,
        'total_realized_profit': total_realized_profit,
        'total_profit_rate': total_profit_rate,
    })


@api_view(['POST'])
@permission_classes([permissions.IsAuthenticated])
def update_strategy_name(request, strategy_id):
    """修改策略名称"""
    try:
        strategy = TradingStrategy.objects.get(id=strategy_id, user=request.user)
        new_name = request.data.get('strategy_name')
        
        if not new_name:
            return Response({'error': '请提供策略名称'}, status=status.HTTP_400_BAD_REQUEST)
        
        strategy.strategy_name = new_name
        strategy.save()
        
        return Response({'message': '策略名称修改成功'}, status=status.HTTP_200_OK)
        
    except TradingStrategy.DoesNotExist:
        return Response({'error': '策略不存在'}, status=status.HTTP_404_NOT_FOUND)
