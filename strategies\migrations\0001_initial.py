# Generated by Django 4.2 on 2025-07-29 06:42

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TradingStrategy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('strategy_name', models.CharField(max_length=100, verbose_name='策略名称')),
                ('strategy_type', models.IntegerField(choices=[(1, '网格交易'), (2, '马丁格尔')], verbose_name='策略类型')),
                ('stock_code', models.CharField(db_index=True, max_length=10, verbose_name='股票代码')),
                ('status', models.IntegerField(choices=[(1, '运行中'), (2, '已停止'), (3, '已完成')], default=1, verbose_name='策略状态')),
                ('grid_min_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='网格最低价')),
                ('grid_max_price', models.DecimalField(blank=True, decimal_places=3, max_digits=10, null=True, verbose_name='网格最高价')),
                ('grid_count', models.IntegerField(blank=True, null=True, verbose_name='网格数量')),
                ('grid_investment', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='网格投资额')),
                ('martin_trigger_rate', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='触发加仓跌幅比例')),
                ('martin_profit_target', models.DecimalField(blank=True, decimal_places=4, max_digits=8, null=True, verbose_name='止盈目标比例')),
                ('martin_initial_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='初次下单金额')),
                ('martin_add_amount', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='加仓单金额')),
                ('martin_max_add_times', models.IntegerField(blank=True, null=True, verbose_name='最大加仓次数')),
                ('martin_current_add_times', models.IntegerField(default=0, verbose_name='当前加仓次数')),
                ('total_investment', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='总投资额')),
                ('realized_profit', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=15, verbose_name='已实现收益')),
                ('realized_profit_rate', models.DecimalField(decimal_places=4, default=Decimal('0.0000'), max_digits=8, verbose_name='已实现收益率')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('start_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='开始时间')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='结束时间')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户')),
            ],
            options={
                'verbose_name': '交易策略',
                'verbose_name_plural': '交易策略',
                'db_table': 'trading_strategies',
            },
        ),
        migrations.CreateModel(
            name='StrategyExecution',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('execution_type', models.IntegerField(choices=[(1, '买入'), (2, '卖出')], verbose_name='执行类型')),
                ('trigger_price', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='触发价格')),
                ('execution_price', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='执行价格')),
                ('quantity', models.IntegerField(verbose_name='数量')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=15, verbose_name='金额')),
                ('profit', models.DecimalField(blank=True, decimal_places=2, max_digits=15, null=True, verbose_name='本次收益')),
                ('execution_time', models.DateTimeField(default=django.utils.timezone.now, verbose_name='执行时间')),
                ('order_id', models.BigIntegerField(blank=True, null=True, verbose_name='关联委托ID')),
                ('strategy', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='strategies.tradingstrategy', verbose_name='策略')),
            ],
            options={
                'verbose_name': '策略执行记录',
                'verbose_name_plural': '策略执行记录',
                'db_table': 'strategy_executions',
            },
        ),
        migrations.CreateModel(
            name='GridLevel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('level', models.IntegerField(verbose_name='层级')),
                ('price', models.DecimalField(decimal_places=3, max_digits=10, verbose_name='价格')),
                ('quantity', models.IntegerField(verbose_name='数量')),
                ('is_buy_filled', models.BooleanField(default=False, verbose_name='买单是否成交')),
                ('is_sell_filled', models.BooleanField(default=False, verbose_name='卖单是否成交')),
                ('buy_order_id', models.BigIntegerField(blank=True, null=True, verbose_name='买单ID')),
                ('sell_order_id', models.BigIntegerField(blank=True, null=True, verbose_name='卖单ID')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('strategy', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='strategies.tradingstrategy', verbose_name='策略')),
            ],
            options={
                'verbose_name': '网格层级',
                'verbose_name_plural': '网格层级',
                'db_table': 'grid_levels',
            },
        ),
        migrations.AddIndex(
            model_name='tradingstrategy',
            index=models.Index(fields=['user'], name='trading_str_user_id_0ccbbd_idx'),
        ),
        migrations.AddIndex(
            model_name='tradingstrategy',
            index=models.Index(fields=['stock_code'], name='trading_str_stock_c_9330b7_idx'),
        ),
        migrations.AddIndex(
            model_name='tradingstrategy',
            index=models.Index(fields=['strategy_type'], name='trading_str_strateg_c56e33_idx'),
        ),
        migrations.AddIndex(
            model_name='tradingstrategy',
            index=models.Index(fields=['status'], name='trading_str_status_fa3577_idx'),
        ),
        migrations.AddIndex(
            model_name='strategyexecution',
            index=models.Index(fields=['strategy'], name='strategy_ex_strateg_f0e9d9_idx'),
        ),
        migrations.AddIndex(
            model_name='strategyexecution',
            index=models.Index(fields=['execution_time'], name='strategy_ex_executi_68a391_idx'),
        ),
        migrations.AddIndex(
            model_name='gridlevel',
            index=models.Index(fields=['strategy'], name='grid_levels_strateg_939fa0_idx'),
        ),
        migrations.AddIndex(
            model_name='gridlevel',
            index=models.Index(fields=['price'], name='grid_levels_price_f17394_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='gridlevel',
            unique_together={('strategy', 'level')},
        ),
    ]
