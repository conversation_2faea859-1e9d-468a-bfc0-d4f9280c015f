from django.db import models
from django.conf import settings
from decimal import Decimal
from django.utils import timezone


class TradingStrategy(models.Model):
    """交易策略"""
    STRATEGY_TYPE_CHOICES = [
        (1, '网格交易'),
        (2, '马丁格尔'),
    ]
    
    STATUS_CHOICES = [
        (1, '运行中'),
        (2, '已停止'),
        (3, '已完成'),
    ]
    
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='用户')
    strategy_name = models.CharField('策略名称', max_length=100)
    strategy_type = models.IntegerField('策略类型', choices=STRATEGY_TYPE_CHOICES)
    stock_code = models.CharField('股票代码', max_length=10, db_index=True)
    status = models.IntegerField('策略状态', choices=STATUS_CHOICES, default=1)
    
    # 网格交易参数
    grid_min_price = models.DecimalField('网格最低价', max_digits=10, decimal_places=3, null=True, blank=True)
    grid_max_price = models.DecimalField('网格最高价', max_digits=10, decimal_places=3, null=True, blank=True)
    grid_count = models.IntegerField('网格数量', null=True, blank=True)
    grid_investment = models.DecimalField('网格投资额', max_digits=15, decimal_places=2, null=True, blank=True)
    
    # 马丁格尔参数
    martin_trigger_rate = models.DecimalField('触发加仓跌幅比例', max_digits=8, decimal_places=4, null=True, blank=True)
    martin_profit_target = models.DecimalField('止盈目标比例', max_digits=8, decimal_places=4, null=True, blank=True)
    martin_initial_amount = models.DecimalField('初次下单金额', max_digits=15, decimal_places=2, null=True, blank=True)
    martin_add_amount = models.DecimalField('加仓单金额', max_digits=15, decimal_places=2, null=True, blank=True)
    martin_max_add_times = models.IntegerField('最大加仓次数', null=True, blank=True)
    martin_current_add_times = models.IntegerField('当前加仓次数', default=0)
    
    total_investment = models.DecimalField('总投资额', max_digits=15, decimal_places=2)
    realized_profit = models.DecimalField('已实现收益', max_digits=15, decimal_places=2, default=Decimal('0.00'))
    realized_profit_rate = models.DecimalField('已实现收益率', max_digits=8, decimal_places=4, default=Decimal('0.0000'))
    
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    start_time = models.DateTimeField('开始时间', default=timezone.now)
    end_time = models.DateTimeField('结束时间', null=True, blank=True)
    
    class Meta:
        db_table = 'trading_strategies'
        verbose_name = '交易策略'
        verbose_name_plural = '交易策略'
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['stock_code']),
            models.Index(fields=['strategy_type']),
            models.Index(fields=['status']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.strategy_name}"
    
    @property
    def running_time(self):
        """运行时间"""
        if self.end_time:
            return self.end_time - self.start_time
        else:
            return timezone.now() - self.start_time
    
    def stop_strategy(self):
        """停止策略"""
        self.status = 2
        self.end_time = timezone.now()
        self.save()


class StrategyExecution(models.Model):
    """策略执行记录"""
    EXECUTION_TYPE_CHOICES = [
        (1, '买入'),
        (2, '卖出'),
    ]
    
    strategy = models.ForeignKey(TradingStrategy, on_delete=models.CASCADE, verbose_name='策略')
    execution_type = models.IntegerField('执行类型', choices=EXECUTION_TYPE_CHOICES)
    trigger_price = models.DecimalField('触发价格', max_digits=10, decimal_places=3)
    execution_price = models.DecimalField('执行价格', max_digits=10, decimal_places=3)
    quantity = models.IntegerField('数量')
    amount = models.DecimalField('金额', max_digits=15, decimal_places=2)
    profit = models.DecimalField('本次收益', max_digits=15, decimal_places=2, null=True, blank=True)
    execution_time = models.DateTimeField('执行时间', default=timezone.now)
    order_id = models.BigIntegerField('关联委托ID', null=True, blank=True)
    
    class Meta:
        db_table = 'strategy_executions'
        verbose_name = '策略执行记录'
        verbose_name_plural = '策略执行记录'
        indexes = [
            models.Index(fields=['strategy']),
            models.Index(fields=['execution_time']),
        ]
    
    def __str__(self):
        return f"{self.strategy.strategy_name} - {self.get_execution_type_display()}"


class GridLevel(models.Model):
    """网格层级"""
    strategy = models.ForeignKey(TradingStrategy, on_delete=models.CASCADE, verbose_name='策略')
    level = models.IntegerField('层级')
    price = models.DecimalField('价格', max_digits=10, decimal_places=3)
    quantity = models.IntegerField('数量')
    is_buy_filled = models.BooleanField('买单是否成交', default=False)
    is_sell_filled = models.BooleanField('卖单是否成交', default=False)
    buy_order_id = models.BigIntegerField('买单ID', null=True, blank=True)
    sell_order_id = models.BigIntegerField('卖单ID', null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        db_table = 'grid_levels'
        verbose_name = '网格层级'
        verbose_name_plural = '网格层级'
        unique_together = [['strategy', 'level']]
        indexes = [
            models.Index(fields=['strategy']),
            models.Index(fields=['price']),
        ]
    
    def __str__(self):
        return f"{self.strategy.strategy_name} - Level {self.level}"
