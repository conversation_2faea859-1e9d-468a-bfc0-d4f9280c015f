from django.db import models
from django.conf import settings
from decimal import Decimal


class Stock(models.Model):
    """股票基础信息"""
    stock_code = models.CharField('股票代码', max_length=10, unique=True)
    stock_name = models.CharField('股票名称', max_length=50)
    market = models.CharField('市场', max_length=10, choices=[('SH', '上海'), ('SZ', '深圳')])
    industry = models.CharField('行业', max_length=50, blank=True, null=True)
    is_active = models.BooleanField('是否活跃', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        db_table = 'stocks'
        verbose_name = '股票'
        verbose_name_plural = '股票'
        indexes = [
            models.Index(fields=['stock_code']),
            models.Index(fields=['stock_name']),
            models.Index(fields=['market']),
        ]
    
    def __str__(self):
        return f"{self.stock_code} - {self.stock_name}"


class StockDailyData(models.Model):
    """股票日线数据"""
    stock_code = models.CharField('股票代码', max_length=10, db_index=True)
    trade_date = models.DateField('交易日期', db_index=True)
    open_price = models.DecimalField('开盘价', max_digits=10, decimal_places=3, null=True, blank=True)
    close_price = models.DecimalField('收盘价', max_digits=10, decimal_places=3)
    high_price = models.DecimalField('最高价', max_digits=10, decimal_places=3, null=True, blank=True)
    low_price = models.DecimalField('最低价', max_digits=10, decimal_places=3, null=True, blank=True)
    volume = models.BigIntegerField('成交量', null=True, blank=True)
    amount = models.DecimalField('成交额', max_digits=15, decimal_places=2, null=True, blank=True)
    change_rate = models.DecimalField('涨跌幅', max_digits=8, decimal_places=4, null=True, blank=True)
    change_amount = models.DecimalField('涨跌额', max_digits=10, decimal_places=3, null=True, blank=True)
    turnover_rate = models.DecimalField('换手率', max_digits=8, decimal_places=4, null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        db_table = 'stock_daily_data'
        verbose_name = '股票日线数据'
        verbose_name_plural = '股票日线数据'
        unique_together = [['stock_code', 'trade_date']]
        indexes = [
            models.Index(fields=['stock_code', 'trade_date']),
            models.Index(fields=['trade_date']),
        ]
    
    def __str__(self):
        return f"{self.stock_code} - {self.trade_date}"


class StockMinuteData(models.Model):
    """股票分钟数据"""
    DATA_TYPE_CHOICES = [
        (1, '历史分钟数据'),
        (2, '当日实时数据'),
    ]
    
    stock_code = models.CharField('股票代码', max_length=10, db_index=True)
    datetime = models.DateTimeField('时间', db_index=True)
    open_price = models.DecimalField('开盘价', max_digits=10, decimal_places=3, null=True, blank=True)
    close_price = models.DecimalField('收盘价', max_digits=10, decimal_places=3)
    high_price = models.DecimalField('最高价', max_digits=10, decimal_places=3, null=True, blank=True)
    low_price = models.DecimalField('最低价', max_digits=10, decimal_places=3, null=True, blank=True)
    volume = models.BigIntegerField('成交量', null=True, blank=True)
    amount = models.DecimalField('成交额', max_digits=15, decimal_places=2, null=True, blank=True)
    change_rate = models.DecimalField('涨跌幅', max_digits=8, decimal_places=4, null=True, blank=True)
    change_amount = models.DecimalField('涨跌额', max_digits=10, decimal_places=3, null=True, blank=True)
    turnover_rate = models.DecimalField('换手率', max_digits=8, decimal_places=4, null=True, blank=True)
    data_type = models.IntegerField('数据类型', choices=DATA_TYPE_CHOICES, default=1)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        db_table = 'stock_minute_data'
        verbose_name = '股票分钟数据'
        verbose_name_plural = '股票分钟数据'
        unique_together = [['stock_code', 'datetime']]
        indexes = [
            models.Index(fields=['stock_code', 'datetime']),
            models.Index(fields=['datetime']),
            models.Index(fields=['data_type']),
        ]
    
    def __str__(self):
        return f"{self.stock_code} - {self.datetime}"


class HS300Data(models.Model):
    """沪深300指数数据"""
    DATA_TYPE_CHOICES = [
        (1, '日线'),
        (2, '分钟线'),
    ]
    
    trade_date = models.DateField('交易日期', db_index=True)
    datetime = models.DateTimeField('具体时间', null=True, blank=True, db_index=True)
    close_price = models.DecimalField('收盘价', max_digits=10, decimal_places=3)
    change_rate = models.DecimalField('涨跌幅', max_digits=8, decimal_places=4, null=True, blank=True)
    data_type = models.IntegerField('数据类型', choices=DATA_TYPE_CHOICES, default=1)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        db_table = 'hs300_data'
        verbose_name = '沪深300数据'
        verbose_name_plural = '沪深300数据'
        unique_together = [['trade_date', 'datetime', 'data_type']]
        indexes = [
            models.Index(fields=['trade_date']),
            models.Index(fields=['datetime']),
        ]
    
    def __str__(self):
        return f"沪深300 - {self.trade_date}"


class UserFavorite(models.Model):
    """用户收藏"""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, verbose_name='用户')
    stock_code = models.CharField('股票代码', max_length=10, db_index=True)
    favorite_date = models.DateField('收藏日期')
    favorite_price = models.DecimalField('收藏时价格', max_digits=10, decimal_places=3, null=True, blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    
    class Meta:
        db_table = 'user_favorites'
        verbose_name = '用户收藏'
        verbose_name_plural = '用户收藏'
        unique_together = [['user', 'stock_code']]
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['stock_code']),
        ]
    
    def __str__(self):
        return f"{self.user.username} - {self.stock_code}"
    
    def get_profit_since_favorite(self):
        """计算从收藏日期到今天的收益率"""
        if not self.favorite_price:
            return None
        
        # 获取最新价格
        latest_data = StockDailyData.objects.filter(
            stock_code=self.stock_code
        ).order_by('-trade_date').first()
        
        if not latest_data:
            return None
        
        current_price = latest_data.close_price
        profit_rate = ((current_price - self.favorite_price) / self.favorite_price) * 100
        return profit_rate
