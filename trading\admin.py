from django.contrib import admin
from .models import TradingOrder, UserPosition, TradingRecord


@admin.register(TradingOrder)
class TradingOrderAdmin(admin.ModelAdmin):
    list_display = ('user', 'stock_code', 'order_type', 'order_price', 'order_quantity', 
                   'filled_quantity', 'order_status', 'order_time')
    list_filter = ('order_type', 'order_status', 'order_time')
    search_fields = ('user__username', 'stock_code')
    ordering = ('-order_time',)


@admin.register(UserPosition)
class UserPositionAdmin(admin.ModelAdmin):
    list_display = ('user', 'stock_code', 'total_quantity', 'avg_cost_price', 
                   'current_price', 'profit_loss', 'profit_loss_rate')
    list_filter = ('first_buy_time', 'last_update_time')
    search_fields = ('user__username', 'stock_code')
    ordering = ('-last_update_time',)


@admin.register(TradingRecord)
class TradingRecordAdmin(admin.ModelAdmin):
    list_display = ('user', 'stock_code', 'trade_type', 'trade_price', 'trade_quantity', 
                   'trade_amount', 'total_fee', 'trade_time')
    list_filter = ('trade_type', 'trade_time')
    search_fields = ('user__username', 'stock_code')
    ordering = ('-trade_time',)
