// 管理员组件
Vue.component('admin-component', {
    template: `
        <div class="admin-component">
            <div class="content-card">
                <div class="card-header">
                    <h3>用户管理</h3>
                    <el-button @click="refreshData" icon="el-icon-refresh">刷新</el-button>
                </div>
                <div class="card-body">
                    <div v-if="loading" class="loading">
                        <i class="el-icon-loading"></i> 加载中...
                    </div>
                    <div v-else>
                        <el-table :data="userList" style="width: 100%">
                            <el-table-column prop="id" label="ID" width="80"></el-table-column>
                            <el-table-column prop="username" label="用户名" width="120"></el-table-column>
                            <el-table-column prop="real_name" label="真实姓名" width="120"></el-table-column>
                            <el-table-column prop="email" label="邮箱" width="180"></el-table-column>
                            <el-table-column prop="phone" label="手机号" width="120"></el-table-column>
                            <el-table-column prop="current_capital" label="当前资金" width="120">
                                <template slot-scope="scope">
                                    {{ scope.row.current_capital | formatMoney }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="total_profit" label="总收益" width="120">
                                <template slot-scope="scope">
                                    <span :class="getPriceColorClass(scope.row.total_profit)">
                                        {{ scope.row.total_profit | formatMoney }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="is_active" label="状态" width="80">
                                <template slot-scope="scope">
                                    <el-tag :type="scope.row.is_active ? 'success' : 'danger'">
                                        {{ scope.row.is_active ? '正常' : '禁用' }}
                                    </el-tag>
                                </template>
                            </el-table-column>
                            <el-table-column prop="created_at" label="注册时间" width="160">
                                <template slot-scope="scope">
                                    {{ scope.row.created_at | formatDateTime }}
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="200">
                                <template slot-scope="scope">
                                    <el-button size="mini" @click="viewUserDetail(scope.row)">详情</el-button>
                                    <el-button size="mini" type="warning" @click="resetUserPassword(scope.row)">重置密码</el-button>
                                    <el-button size="mini" type="danger" @click="deleteUser(scope.row)" 
                                               :disabled="scope.row.is_admin">删除</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </div>
            </div>

            <!-- 用户详情对话框 -->
            <el-dialog title="用户详情" :visible.sync="userDetailDialogVisible" width="600px">
                <div v-if="selectedUser">
                    <el-descriptions :column="2" border>
                        <el-descriptions-item label="用户ID">{{ selectedUser.id }}</el-descriptions-item>
                        <el-descriptions-item label="用户名">{{ selectedUser.username }}</el-descriptions-item>
                        <el-descriptions-item label="真实姓名">{{ selectedUser.real_name || '--' }}</el-descriptions-item>
                        <el-descriptions-item label="邮箱">{{ selectedUser.email || '--' }}</el-descriptions-item>
                        <el-descriptions-item label="手机号">{{ selectedUser.phone || '--' }}</el-descriptions-item>
                        <el-descriptions-item label="是否管理员">
                            <el-tag :type="selectedUser.is_admin ? 'success' : 'info'">
                                {{ selectedUser.is_admin ? '是' : '否' }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="账户状态">
                            <el-tag :type="selectedUser.is_active ? 'success' : 'danger'">
                                {{ selectedUser.is_active ? '正常' : '禁用' }}
                            </el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="当前资金">
                            {{ selectedUser.current_capital | formatMoney }}
                        </el-descriptions-item>
                        <el-descriptions-item label="总收益">
                            <span :class="getPriceColorClass(selectedUser.total_profit)">
                                {{ selectedUser.total_profit | formatMoney }}
                            </span>
                        </el-descriptions-item>
                        <el-descriptions-item label="注册时间">
                            {{ selectedUser.created_at | formatDateTime }}
                        </el-descriptions-item>
                    </el-descriptions>
                </div>
            </el-dialog>

            <!-- 重置密码对话框 -->
            <el-dialog title="重置用户密码" :visible.sync="resetPasswordDialogVisible" width="400px">
                <el-form :model="resetPasswordForm" :rules="resetPasswordRules" ref="resetPasswordForm" label-width="100px">
                    <el-form-item label="用户名">
                        <el-input v-model="resetPasswordUser.username" disabled></el-input>
                    </el-form-item>
                    <el-form-item label="新密码" prop="new_password">
                        <el-input v-model="resetPasswordForm.new_password" type="password" placeholder="请输入新密码"></el-input>
                    </el-form-item>
                    <el-form-item label="确认密码" prop="confirm_password">
                        <el-input v-model="resetPasswordForm.confirm_password" type="password" placeholder="请再次输入新密码"></el-input>
                    </el-form-item>
                </el-form>
                <div slot="footer">
                    <el-button @click="resetPasswordDialogVisible = false">取消</el-button>
                    <el-button type="primary" @click="confirmResetPassword" :loading="resetPasswordLoading">确定</el-button>
                </div>
            </el-dialog>
        </div>
    `,
    
    data() {
        return {
            loading: false,
            userList: [],
            userDetailDialogVisible: false,
            selectedUser: null,
            resetPasswordDialogVisible: false,
            resetPasswordUser: {},
            resetPasswordForm: {
                new_password: '',
                confirm_password: ''
            },
            resetPasswordRules: {
                new_password: [
                    { required: true, message: '请输入新密码', trigger: 'blur' },
                    { min: 6, message: '密码长度至少 6 个字符', trigger: 'blur' }
                ],
                confirm_password: [
                    { required: true, message: '请再次输入新密码', trigger: 'blur' },
                    { validator: this.validateConfirmPassword, trigger: 'blur' }
                ]
            },
            resetPasswordLoading: false
        };
    },
    
    mounted() {
        this.loadUserList();
    },
    
    methods: {
        // 加载用户列表
        async loadUserList() {
            this.loading = true;
            const result = await API.admin.getUserList();
            this.loading = false;
            
            if (result.success) {
                this.userList = result.data.results || result.data;
            } else {
                this.showError('加载用户列表失败: ' + result.error);
            }
        },
        
        // 查看用户详情
        viewUserDetail(user) {
            this.selectedUser = user;
            this.userDetailDialogVisible = true;
        },
        
        // 重置用户密码
        resetUserPassword(user) {
            this.resetPasswordUser = user;
            this.resetPasswordDialogVisible = true;
        },
        
        // 确认重置密码
        async confirmResetPassword() {
            this.$refs.resetPasswordForm.validate(async (valid) => {
                if (valid) {
                    this.resetPasswordLoading = true;
                    const result = await API.admin.resetUserPassword(
                        this.resetPasswordUser.id,
                        this.resetPasswordForm.new_password
                    );
                    this.resetPasswordLoading = false;
                    
                    if (result.success) {
                        this.showSuccess('密码重置成功');
                        this.resetPasswordDialogVisible = false;
                        this.$refs.resetPasswordForm.resetFields();
                    } else {
                        this.showError('密码重置失败: ' + result.error);
                    }
                }
            });
        },
        
        // 验证确认密码
        validateConfirmPassword(rule, value, callback) {
            if (value !== this.resetPasswordForm.new_password) {
                callback(new Error('两次输入的密码不一致'));
            } else {
                callback();
            }
        },
        
        // 删除用户
        async deleteUser(user) {
            if (user.is_admin) {
                this.showWarning('不能删除管理员账户');
                return;
            }
            
            try {
                await this.confirmAction(\`确定要删除用户 "\${user.username}" 吗？此操作不可恢复！\`);
                const result = await API.admin.deleteUser(user.id);
                if (result.success) {
                    this.showSuccess('用户删除成功');
                    this.loadUserList(); // 重新加载列表
                } else {
                    this.showError('用户删除失败: ' + result.error);
                }
            } catch (error) {
                // 用户取消操作
            }
        },
        
        // 获取价格颜色类
        getPriceColorClass(value) {
            return Utils.getPriceColorClass(value);
        },
        
        // 刷新数据
        refreshData() {
            this.loadUserList();
        }
    }
});
