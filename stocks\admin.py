from django.contrib import admin
from .models import Stock, StockDailyData, StockMinuteData, HS300Data, UserFavorite


@admin.register(Stock)
class StockAdmin(admin.ModelAdmin):
    list_display = ('stock_code', 'stock_name', 'market', 'industry', 'is_active', 'created_at')
    list_filter = ('market', 'industry', 'is_active')
    search_fields = ('stock_code', 'stock_name')
    ordering = ('stock_code',)


@admin.register(StockDailyData)
class StockDailyDataAdmin(admin.ModelAdmin):
    list_display = ('stock_code', 'trade_date', 'close_price', 'change_rate', 'volume')
    list_filter = ('trade_date',)
    search_fields = ('stock_code',)
    ordering = ('-trade_date', 'stock_code')


@admin.register(StockMinuteData)
class StockMinuteDataAdmin(admin.ModelAdmin):
    list_display = ('stock_code', 'datetime', 'close_price', 'change_rate', 'data_type')
    list_filter = ('data_type', 'datetime')
    search_fields = ('stock_code',)
    ordering = ('-datetime', 'stock_code')


@admin.register(HS300Data)
class HS300DataAdmin(admin.ModelAdmin):
    list_display = ('trade_date', 'datetime', 'close_price', 'change_rate', 'data_type')
    list_filter = ('data_type', 'trade_date')
    ordering = ('-trade_date', '-datetime')


@admin.register(UserFavorite)
class UserFavoriteAdmin(admin.ModelAdmin):
    list_display = ('user', 'stock_code', 'favorite_date', 'favorite_price', 'created_at')
    list_filter = ('favorite_date', 'created_at')
    search_fields = ('user__username', 'stock_code')
    ordering = ('-created_at',)
