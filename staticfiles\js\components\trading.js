// 交易组件
Vue.component('trading-component', {
    template: `
        <div class="trading-component">
            <!-- 收益汇总 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ profitSummary.total_assets | formatMoney }}</div>
                    <div class="stat-label">总资产</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ profitSummary.current_capital | formatMoney }}</div>
                    <div class="stat-label">可用资金</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ profitSummary.total_market_value | formatMoney }}</div>
                    <div class="stat-label">持仓市值</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" :class="getPriceColorClass(profitSummary.total_profit)">
                        {{ profitSummary.total_profit | formatMoney }}
                    </div>
                    <div class="stat-label">总收益</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" :class="getPriceColorClass(profitSummary.total_profit_rate)">
                        {{ profitSummary.total_profit_rate | formatPercent }}
                    </div>
                    <div class="stat-label">总收益率</div>
                </div>
            </div>

            <!-- 交易表单 -->
            <div class="content-card">
                <div class="card-header">
                    <h3>股票交易</h3>
                </div>
                <div class="card-body">
                    <el-form :model="tradeForm" :rules="tradeRules" ref="tradeForm" label-width="100px">
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="股票代码" prop="stock_code">
                                    <el-input v-model="tradeForm.stock_code" placeholder="请输入股票代码"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="交易类型" prop="order_type">
                                    <el-select v-model="tradeForm.order_type" placeholder="请选择">
                                        <el-option label="买入" :value="1"></el-option>
                                        <el-option label="卖出" :value="2"></el-option>
                                    </el-select>
                                </el-form-item>
                            </el-col>
                            <el-col :span="8">
                                <el-form-item label="委托价格" prop="order_price">
                                    <el-input v-model="tradeForm.order_price" placeholder="请输入价格"></el-input>
                                </el-form-item>
                            </el-col>
                        </el-row>
                        <el-row :gutter="20">
                            <el-col :span="8">
                                <el-form-item label="委托数量" prop="order_quantity">
                                    <el-input v-model="tradeForm.order_quantity" placeholder="请输入数量"></el-input>
                                </el-form-item>
                            </el-col>
                            <el-col :span="16">
                                <el-form-item>
                                    <el-button type="primary" @click="submitTrade" :loading="tradeLoading">提交委托</el-button>
                                    <el-button @click="resetForm">重置</el-button>
                                </el-form-item>
                            </el-col>
                        </el-row>
                    </el-form>
                </div>
            </div>

            <!-- 标签页 -->
            <el-tabs v-model="activeTab" @tab-click="handleTabClick">
                <!-- 当前委托 -->
                <el-tab-pane label="当前委托" name="current_orders">
                    <div class="data-table">
                        <el-table :data="currentOrders" style="width: 100%">
                            <el-table-column prop="stock_code" label="股票代码" width="100"></el-table-column>
                            <el-table-column prop="stock_name" label="股票名称" width="120"></el-table-column>
                            <el-table-column prop="order_type_display" label="类型" width="80"></el-table-column>
                            <el-table-column prop="order_price" label="委托价格" width="100">
                                <template slot-scope="scope">
                                    {{ scope.row.order_price | formatNumber(3) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="order_quantity" label="委托数量" width="100"></el-table-column>
                            <el-table-column prop="filled_quantity" label="成交数量" width="100"></el-table-column>
                            <el-table-column prop="order_status_display" label="状态" width="100"></el-table-column>
                            <el-table-column prop="order_time" label="委托时间" width="160">
                                <template slot-scope="scope">
                                    {{ scope.row.order_time | formatDateTime }}
                                </template>
                            </el-table-column>
                            <el-table-column label="操作" width="100">
                                <template slot-scope="scope">
                                    <el-button size="mini" type="danger" @click="cancelOrder(scope.row.id)">撤销</el-button>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <!-- 历史委托 -->
                <el-tab-pane label="历史委托" name="history_orders">
                    <div class="data-table">
                        <el-table :data="historyOrders" style="width: 100%">
                            <el-table-column prop="stock_code" label="股票代码" width="100"></el-table-column>
                            <el-table-column prop="stock_name" label="股票名称" width="120"></el-table-column>
                            <el-table-column prop="order_type_display" label="类型" width="80"></el-table-column>
                            <el-table-column prop="order_price" label="委托价格" width="100">
                                <template slot-scope="scope">
                                    {{ scope.row.order_price | formatNumber(3) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="order_quantity" label="委托数量" width="100"></el-table-column>
                            <el-table-column prop="filled_quantity" label="成交数量" width="100"></el-table-column>
                            <el-table-column prop="order_status_display" label="状态" width="100"></el-table-column>
                            <el-table-column prop="order_time" label="委托时间" width="160">
                                <template slot-scope="scope">
                                    {{ scope.row.order_time | formatDateTime }}
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <!-- 当前持仓 -->
                <el-tab-pane label="当前持仓" name="current_positions">
                    <div class="data-table">
                        <el-table :data="currentPositions" style="width: 100%">
                            <el-table-column prop="stock_code" label="股票代码" width="100"></el-table-column>
                            <el-table-column prop="stock_name" label="股票名称" width="120"></el-table-column>
                            <el-table-column prop="total_quantity" label="持仓数量" width="100"></el-table-column>
                            <el-table-column prop="avg_cost_price" label="成本价" width="100">
                                <template slot-scope="scope">
                                    {{ scope.row.avg_cost_price | formatNumber(3) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="current_price" label="现价" width="100">
                                <template slot-scope="scope">
                                    {{ scope.row.current_price | formatNumber(3) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="market_value" label="市值" width="120">
                                <template slot-scope="scope">
                                    {{ scope.row.market_value | formatMoney }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="profit_loss" label="盈亏" width="120">
                                <template slot-scope="scope">
                                    <span :class="getPriceColorClass(scope.row.profit_loss)">
                                        {{ scope.row.profit_loss | formatMoney }}
                                    </span>
                                </template>
                            </el-table-column>
                            <el-table-column prop="profit_loss_rate" label="盈亏率" width="100">
                                <template slot-scope="scope">
                                    <span :class="getPriceColorClass(scope.row.profit_loss_rate)">
                                        {{ scope.row.profit_loss_rate | formatPercent }}
                                    </span>
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>

                <!-- 交易记录 -->
                <el-tab-pane label="交易记录" name="trading_records">
                    <div class="data-table">
                        <el-table :data="tradingRecords" style="width: 100%">
                            <el-table-column prop="stock_code" label="股票代码" width="100"></el-table-column>
                            <el-table-column prop="stock_name" label="股票名称" width="120"></el-table-column>
                            <el-table-column prop="trade_type_display" label="类型" width="80"></el-table-column>
                            <el-table-column prop="trade_price" label="成交价格" width="100">
                                <template slot-scope="scope">
                                    {{ scope.row.trade_price | formatNumber(3) }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="trade_quantity" label="成交数量" width="100"></el-table-column>
                            <el-table-column prop="trade_amount" label="成交金额" width="120">
                                <template slot-scope="scope">
                                    {{ scope.row.trade_amount | formatMoney }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="total_fee" label="手续费" width="100">
                                <template slot-scope="scope">
                                    {{ scope.row.total_fee | formatMoney }}
                                </template>
                            </el-table-column>
                            <el-table-column prop="trade_time" label="成交时间" width="160">
                                <template slot-scope="scope">
                                    {{ scope.row.trade_time | formatDateTime }}
                                </template>
                            </el-table-column>
                        </el-table>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    `,
    
    data() {
        return {
            activeTab: 'current_orders',
            profitSummary: {
                total_assets: 0,
                current_capital: 0,
                total_market_value: 0,
                total_profit: 0,
                total_profit_rate: 0
            },
            tradeForm: {
                stock_code: '',
                order_type: 1,
                order_price: '',
                order_quantity: ''
            },
            tradeRules: {
                stock_code: [
                    { required: true, message: '请输入股票代码', trigger: 'blur' }
                ],
                order_type: [
                    { required: true, message: '请选择交易类型', trigger: 'change' }
                ],
                order_price: [
                    { required: true, message: '请输入委托价格', trigger: 'blur' }
                ],
                order_quantity: [
                    { required: true, message: '请输入委托数量', trigger: 'blur' }
                ]
            },
            tradeLoading: false,
            currentOrders: [],
            historyOrders: [],
            currentPositions: [],
            tradingRecords: []
        };
    },
    
    mounted() {
        this.loadProfitSummary();
        this.loadCurrentOrders();
    },
    
    methods: {
        // 加载收益汇总
        async loadProfitSummary() {
            const result = await API.trading.getProfitSummary();
            if (result.success) {
                this.profitSummary = result.data;
            }
        },
        
        // 提交交易
        async submitTrade() {
            this.$refs.tradeForm.validate(async (valid) => {
                if (valid) {
                    this.tradeLoading = true;
                    const result = await API.trading.createOrder(this.tradeForm);
                    this.tradeLoading = false;
                    
                    if (result.success) {
                        this.showSuccess('委托创建成功');
                        this.resetForm();
                        this.loadCurrentOrders();
                        this.loadProfitSummary();
                    } else {
                        this.showError('委托创建失败: ' + result.error);
                    }
                }
            });
        },
        
        // 重置表单
        resetForm() {
            this.$refs.tradeForm.resetFields();
        },
        
        // 处理标签页切换
        handleTabClick(tab) {
            switch (tab.name) {
                case 'current_orders':
                    this.loadCurrentOrders();
                    break;
                case 'history_orders':
                    this.loadHistoryOrders();
                    break;
                case 'current_positions':
                    this.loadCurrentPositions();
                    break;
                case 'trading_records':
                    this.loadTradingRecords();
                    break;
            }
        },
        
        // 加载当前委托
        async loadCurrentOrders() {
            const result = await API.trading.getCurrentOrders();
            if (result.success) {
                this.currentOrders = result.data.results || result.data;
            }
        },
        
        // 加载历史委托
        async loadHistoryOrders() {
            const result = await API.trading.getHistoryOrders();
            if (result.success) {
                this.historyOrders = result.data.results || result.data;
            }
        },
        
        // 加载当前持仓
        async loadCurrentPositions() {
            const result = await API.trading.getCurrentPositions();
            if (result.success) {
                this.currentPositions = result.data.results || result.data;
            }
        },
        
        // 加载交易记录
        async loadTradingRecords() {
            const result = await API.trading.getTradingRecords();
            if (result.success) {
                this.tradingRecords = result.data.results || result.data;
            }
        },
        
        // 撤销委托
        async cancelOrder(orderId) {
            try {
                await this.confirmAction('确定要撤销这个委托吗？');
                const result = await API.trading.cancelOrder(orderId);
                if (result.success) {
                    this.showSuccess('撤销成功');
                    this.loadCurrentOrders();
                } else {
                    this.showError('撤销失败: ' + result.error);
                }
            } catch (error) {
                // 用户取消操作
            }
        },
        
        // 获取价格颜色类
        getPriceColorClass(value) {
            return Utils.getPriceColorClass(value);
        },
        
        // 刷新数据
        refreshData() {
            this.loadProfitSummary();
            this.handleTabClick({ name: this.activeTab });
        }
    }
});
