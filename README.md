# 股票池自选管理系统

这是一个基于Django + Vue.js开发的股票池自选管理系统，支持股票数据查看、收藏管理、模拟交易和自动化交易策略。

## 功能特性

### 主要功能模块

1. **主界面**
   - 股票涨幅排行榜（年度/月度）
   - 股票搜索功能
   - 股票详情查看
   - K线图表展示
   - 沪深300对比图表

2. **收藏功能**
   - 股票收藏/取消收藏
   - 收藏列表管理
   - 收藏收益统计

3. **模拟交易**
   - 股票买卖交易
   - 委托管理（当前/历史）
   - 持仓管理（当前/历史）
   - 交易记录查询
   - 收益统计分析

4. **交易策略**
   - 网格交易策略
   - 马丁格尔策略
   - 策略监控和管理
   - 策略收益统计

5. **个人中心**
   - 用户信息管理
   - 密码修改
   - 账户切换
   - 资金统计

6. **系统管理**（管理员功能）
   - 用户管理
   - 密码重置
   - 用户删除

## 技术架构

### 后端技术栈
- **框架**: Django 4.2
- **API**: Django REST Framework
- **数据库**: MySQL
- **数据源**: 
  - AKShare (股票数据)
  - Tushare (历史数据)

### 前端技术栈
- **框架**: Vue.js 2.x
- **UI组件**: Element UI
- **图表**: ECharts
- **HTTP客户端**: Axios

## 项目结构

```
stock_management/
├── stock_management/          # Django项目配置
│   ├── settings.py           # 项目设置
│   ├── urls.py              # URL路由
│   └── views.py             # 视图函数
├── accounts/                 # 用户管理应用
│   ├── models.py            # 用户模型
│   ├── views.py             # 用户视图
│   ├── serializers.py       # 序列化器
│   └── urls.py              # URL路由
├── stocks/                   # 股票管理应用
│   ├── models.py            # 股票模型
│   ├── views.py             # 股票视图
│   ├── services.py          # 股票服务
│   └── serializers.py       # 序列化器
├── trading/                  # 交易管理应用
│   ├── models.py            # 交易模型
│   ├── views.py             # 交易视图
│   ├── services.py          # 交易服务
│   └── serializers.py       # 序列化器
├── strategies/               # 策略管理应用
│   ├── models.py            # 策略模型
│   ├── views.py             # 策略视图
│   ├── services.py          # 策略服务
│   └── serializers.py       # 序列化器
├── templates/                # 前端模板
│   └── index.html           # 主页面
├── static/                   # 静态文件
│   ├── css/                 # 样式文件
│   ├── js/                  # JavaScript文件
│   └── images/              # 图片文件
├── database_schema.sql       # 数据库设计
├── requirements.txt          # Python依赖
└── README.md                # 项目说明
```

## 安装部署

### 1. 环境要求
- Python 3.8+
- MySQL 5.7+
- Node.js (可选，用于前端开发)

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 数据库配置
1. 创建MySQL数据库
2. 执行 `database_schema.sql` 创建表结构
3. 修改 `stock_management/settings.py` 中的数据库配置

### 4. 运行项目
```bash
python manage.py runserver
```

访问 http://localhost:8000 即可使用系统。

## 默认账户

- **管理员账户**: admin / admin
- **普通用户**: 需要注册创建

## 数据源配置

### Tushare配置
在 `stock_management/settings.py` 中配置Tushare Token：
```python
STOCK_DATA_CONFIG = {
    'TUSHARE_TOKEN': '你的Tushare Token',
    # ...
}
```

### 数据获取脚本
- `daily.py`: 获取日线数据
- `minute.py`: 获取分钟数据
- `data.py`: 数据处理示例

## 交易费用设置

系统模拟真实交易费用：
- **买卖手续费**: 0.1641‰ (0.01641%)
- **印花税**: 0.5‰ (0.05%，仅卖出收取)

## 主要API接口

### 用户认证
- `POST /api/accounts/login/` - 用户登录
- `POST /api/accounts/register/` - 用户注册
- `GET /api/accounts/profile/` - 获取用户信息

### 股票数据
- `GET /api/stocks/ranking/` - 股票排行
- `GET /api/stocks/search/` - 股票搜索
- `GET /api/stocks/{code}/` - 股票详情
- `GET /api/stocks/{code}/chart/` - 股票图表数据

### 交易功能
- `POST /api/trading/orders/create/` - 创建委托
- `GET /api/trading/positions/current/` - 当前持仓
- `GET /api/trading/records/` - 交易记录

### 策略管理
- `POST /api/strategies/grid/create/` - 创建网格策略
- `POST /api/strategies/martin/create/` - 创建马丁格尔策略
- `GET /api/strategies/running/` - 运行中策略

## 注意事项

1. **数据源**: 项目使用AKShare和Tushare获取股票数据，需要稳定的网络连接
2. **实时性**: 分钟级数据更新频率取决于数据源的更新频率
3. **模拟交易**: 所有交易均为模拟，不涉及真实资金
4. **策略风险**: 自动化交易策略仅供学习参考，实际投资需谨慎

## 开发说明

### 添加新功能
1. 在对应的Django应用中添加模型、视图、序列化器
2. 在前端添加对应的Vue组件
3. 更新API接口文档

### 数据库迁移
```bash
python manage.py makemigrations
python manage.py migrate
```

### 静态文件收集
```bash
python manage.py collectstatic
```

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请通过GitHub Issues联系。
