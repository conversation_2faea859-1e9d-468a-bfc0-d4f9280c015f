from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from django.conf import settings
from .models import TradingOrder, UserPosition, TradingRecord
from stocks.services import StockDataService
import logging

logger = logging.getLogger(__name__)


class TradingService:
    """交易服务"""
    
    def __init__(self):
        self.stock_service = StockDataService()
    
    def create_order(self, user, stock_code, order_type, order_price, order_quantity, strategy_id=None):
        """创建委托单"""
        try:
            with transaction.atomic():
                # 验证参数
                if order_quantity <= 0:
                    return {'success': False, 'message': '委托数量必须大于0'}
                
                if order_price <= 0:
                    return {'success': False, 'message': '委托价格必须大于0'}
                
                # 买入时检查资金是否充足
                if order_type == 1:  # 买入
                    required_amount = order_price * order_quantity
                    # 计算手续费
                    commission_fee = required_amount * Decimal(str(settings.STOCK_DATA_CONFIG['TRADING_FEE_RATE']))
                    total_required = required_amount + commission_fee
                    
                    if user.current_capital < total_required:
                        return {'success': False, 'message': '资金不足'}
                
                # 卖出时检查持仓是否充足
                elif order_type == 2:  # 卖出
                    try:
                        position = UserPosition.objects.get(user=user, stock_code=stock_code)
                        if position.available_quantity < order_quantity:
                            return {'success': False, 'message': '持仓不足'}
                    except UserPosition.DoesNotExist:
                        return {'success': False, 'message': '无持仓'}
                
                # 创建委托单
                order = TradingOrder.objects.create(
                    user=user,
                    stock_code=stock_code,
                    order_type=order_type,
                    order_price=order_price,
                    order_quantity=order_quantity,
                    strategy_id=strategy_id
                )
                
                # 尝试立即成交
                self.try_fill_order(order)
                
                return {'success': True, 'order': order}
                
        except Exception as e:
            logger.error(f"创建委托单失败: {e}")
            return {'success': False, 'message': str(e)}
    
    def try_fill_order(self, order):
        """尝试成交委托单"""
        try:
            # 获取当前市场价格
            current_price = self.stock_service.get_current_price(order.stock_code)
            if not current_price:
                return False
            
            # 简单的成交逻辑：如果委托价格合理就立即成交
            can_fill = False
            if order.order_type == 1:  # 买入
                can_fill = current_price <= order.order_price
            else:  # 卖出
                can_fill = current_price >= order.order_price
            
            if can_fill:
                return self.fill_order(order, current_price, order.order_quantity)
            
            return False
            
        except Exception as e:
            logger.error(f"尝试成交委托单失败: {e}")
            return False
    
    def fill_order(self, order, fill_price, fill_quantity):
        """成交委托单"""
        try:
            with transaction.atomic():
                # 创建交易记录
                trade_record = TradingRecord.objects.create(
                    user=order.user,
                    stock_code=order.stock_code,
                    trade_type=order.order_type,
                    trade_price=fill_price,
                    trade_quantity=fill_quantity,
                    order=order,
                    strategy_id=order.strategy_id
                )
                
                # 更新委托单状态
                order.filled_quantity += fill_quantity
                order.commission_fee += trade_record.commission_fee
                
                if order.filled_quantity >= order.order_quantity:
                    order.order_status = 3  # 全部成交
                    order.filled_time = timezone.now()
                else:
                    order.order_status = 2  # 部分成交
                
                order.save()
                
                # 更新用户资金和持仓
                if order.order_type == 1:  # 买入
                    self.update_position_buy(order.user, order.stock_code, fill_price, fill_quantity)
                    # 扣除资金
                    order.user.update_capital(-trade_record.net_amount)
                else:  # 卖出
                    self.update_position_sell(order.user, order.stock_code, fill_quantity)
                    # 增加资金
                    order.user.update_capital(trade_record.net_amount)
                
                return True
                
        except Exception as e:
            logger.error(f"成交委托单失败: {e}")
            return False
    
    def update_position_buy(self, user, stock_code, buy_price, buy_quantity):
        """更新买入持仓"""
        try:
            position, created = UserPosition.objects.get_or_create(
                user=user,
                stock_code=stock_code,
                defaults={
                    'total_quantity': 0,
                    'available_quantity': 0,
                    'avg_cost_price': buy_price,
                    'total_cost': Decimal('0.00'),
                    'first_buy_time': timezone.now()
                }
            )
            
            # 计算新的平均成本价
            old_total_cost = position.total_cost
            old_quantity = position.total_quantity
            
            new_cost = buy_price * buy_quantity
            new_total_cost = old_total_cost + new_cost
            new_total_quantity = old_quantity + buy_quantity
            
            position.avg_cost_price = new_total_cost / new_total_quantity
            position.total_cost = new_total_cost
            position.total_quantity = new_total_quantity
            position.available_quantity = new_total_quantity
            
            if created:
                position.first_buy_time = timezone.now()
            
            # 更新市值
            position.update_market_value(buy_price)
            
        except Exception as e:
            logger.error(f"更新买入持仓失败: {e}")
    
    def update_position_sell(self, user, stock_code, sell_quantity):
        """更新卖出持仓"""
        try:
            position = UserPosition.objects.get(user=user, stock_code=stock_code)
            
            # 减少持仓
            position.total_quantity -= sell_quantity
            position.available_quantity -= sell_quantity
            
            # 如果全部卖出，重置成本
            if position.total_quantity <= 0:
                position.total_quantity = 0
                position.available_quantity = 0
                position.total_cost = Decimal('0.00')
                position.avg_cost_price = Decimal('0.000')
            else:
                # 按比例减少总成本
                cost_ratio = sell_quantity / (position.total_quantity + sell_quantity)
                position.total_cost *= (1 - cost_ratio)
            
            position.save()
            
        except UserPosition.DoesNotExist:
            logger.error(f"卖出时找不到持仓: {user.username} - {stock_code}")
        except Exception as e:
            logger.error(f"更新卖出持仓失败: {e}")
    
    def cancel_order(self, order_id, user):
        """撤销委托单"""
        try:
            order = TradingOrder.objects.get(id=order_id, user=user)
            
            if order.order_status in [3, 4]:  # 已成交或已撤销
                return {'success': False, 'message': '委托单无法撤销'}
            
            order.order_status = 4  # 已撤销
            order.save()
            
            return {'success': True, 'message': '撤销成功'}
            
        except TradingOrder.DoesNotExist:
            return {'success': False, 'message': '委托单不存在'}
        except Exception as e:
            logger.error(f"撤销委托单失败: {e}")
            return {'success': False, 'message': str(e)}
    
    def update_all_positions_price(self, user):
        """更新用户所有持仓的当前价格"""
        try:
            positions = UserPosition.objects.filter(user=user, total_quantity__gt=0)
            
            for position in positions:
                current_price = self.stock_service.get_current_price(position.stock_code)
                if current_price:
                    position.update_market_value(current_price)
            
        except Exception as e:
            logger.error(f"更新持仓价格失败: {e}")
    
    def get_user_profit_summary(self, user):
        """获取用户收益汇总"""
        try:
            # 更新所有持仓价格
            self.update_all_positions_price(user)
            
            # 计算持仓市值
            positions = UserPosition.objects.filter(user=user, total_quantity__gt=0)
            total_market_value = sum(pos.market_value or 0 for pos in positions)
            total_position_profit = sum(pos.profit_loss or 0 for pos in positions)
            
            # 计算已实现收益（从交易记录计算）
            buy_records = TradingRecord.objects.filter(user=user, trade_type=1)
            sell_records = TradingRecord.objects.filter(user=user, trade_type=2)
            
            total_buy_amount = sum(record.net_amount for record in buy_records)
            total_sell_amount = sum(record.net_amount for record in sell_records)
            realized_profit = total_sell_amount - total_buy_amount
            
            # 总资产 = 现金 + 持仓市值
            total_assets = user.current_capital + total_market_value
            
            # 总收益 = 总资产 - 初始资金
            total_profit = total_assets - user.initial_capital
            
            # 总收益率
            total_profit_rate = (total_profit / user.initial_capital * 100) if user.initial_capital > 0 else 0
            
            return {
                'total_assets': total_assets,
                'current_capital': user.current_capital,
                'total_market_value': total_market_value,
                'total_profit': total_profit,
                'total_profit_rate': total_profit_rate,
                'realized_profit': realized_profit,
                'unrealized_profit': total_position_profit,
            }
            
        except Exception as e:
            logger.error(f"获取用户收益汇总失败: {e}")
            return None
