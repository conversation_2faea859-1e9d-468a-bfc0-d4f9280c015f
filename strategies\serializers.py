from rest_framework import serializers
from .models import TradingStrategy, StrategyExecution, GridLevel
from stocks.models import Stock


class CreateGridStrategySerializer(serializers.Serializer):
    """创建网格策略序列化器"""
    strategy_name = serializers.CharField(max_length=100)
    stock_code = serializers.CharField(max_length=10)
    grid_min_price = serializers.DecimalField(max_digits=10, decimal_places=3, min_value=0.001)
    grid_max_price = serializers.DecimalField(max_digits=10, decimal_places=3, min_value=0.001)
    grid_count = serializers.IntegerField(min_value=2, max_value=50)
    grid_investment = serializers.DecimalField(max_digits=15, decimal_places=2, min_value=0.01)
    
    def validate_stock_code(self, value):
        try:
            Stock.objects.get(stock_code=value)
            return value
        except Stock.DoesNotExist:
            raise serializers.ValidationError("股票代码不存在")
    
    def validate(self, attrs):
        if attrs['grid_min_price'] >= attrs['grid_max_price']:
            raise serializers.ValidationError("最低价必须小于最高价")
        return attrs


class CreateMartinStrategySerializer(serializers.Serializer):
    """创建马丁格尔策略序列化器"""
    strategy_name = serializers.CharField(max_length=100)
    stock_code = serializers.CharField(max_length=10)
    martin_trigger_rate = serializers.DecimalField(max_digits=8, decimal_places=4, min_value=0.01, max_value=99.99)
    martin_profit_target = serializers.DecimalField(max_digits=8, decimal_places=4, min_value=0.01)
    martin_initial_amount = serializers.DecimalField(max_digits=15, decimal_places=2, min_value=0.01)
    martin_add_amount = serializers.DecimalField(max_digits=15, decimal_places=2, min_value=0.01)
    martin_max_add_times = serializers.IntegerField(min_value=1, max_value=10)
    
    def validate_stock_code(self, value):
        try:
            Stock.objects.get(stock_code=value)
            return value
        except Stock.DoesNotExist:
            raise serializers.ValidationError("股票代码不存在")


class TradingStrategySerializer(serializers.ModelSerializer):
    """交易策略序列化器"""
    strategy_type_display = serializers.CharField(source='get_strategy_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    stock_name = serializers.SerializerMethodField()
    running_time_display = serializers.SerializerMethodField()
    
    class Meta:
        model = TradingStrategy
        fields = ('id', 'strategy_name', 'strategy_type', 'strategy_type_display',
                 'stock_code', 'stock_name', 'status', 'status_display',
                 'total_investment', 'realized_profit', 'realized_profit_rate',
                 'created_at', 'start_time', 'end_time', 'running_time_display',
                 # 网格参数
                 'grid_min_price', 'grid_max_price', 'grid_count', 'grid_investment',
                 # 马丁格尔参数
                 'martin_trigger_rate', 'martin_profit_target', 'martin_initial_amount',
                 'martin_add_amount', 'martin_max_add_times', 'martin_current_add_times')
        read_only_fields = ('id', 'realized_profit', 'realized_profit_rate', 'created_at',
                           'start_time', 'end_time', 'martin_current_add_times')
    
    def get_stock_name(self, obj):
        try:
            stock = Stock.objects.get(stock_code=obj.stock_code)
            return stock.stock_name
        except Stock.DoesNotExist:
            return None
    
    def get_running_time_display(self, obj):
        running_time = obj.running_time
        if running_time:
            days = running_time.days
            hours, remainder = divmod(running_time.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            return f"{days}天{hours}小时{minutes}分钟"
        return "0分钟"


class StrategyExecutionSerializer(serializers.ModelSerializer):
    """策略执行记录序列化器"""
    execution_type_display = serializers.CharField(source='get_execution_type_display', read_only=True)
    
    class Meta:
        model = StrategyExecution
        fields = ('id', 'execution_type', 'execution_type_display', 'trigger_price',
                 'execution_price', 'quantity', 'amount', 'profit', 'execution_time')
        read_only_fields = ('id',)


class GridLevelSerializer(serializers.ModelSerializer):
    """网格层级序列化器"""
    
    class Meta:
        model = GridLevel
        fields = ('id', 'level', 'price', 'quantity', 'is_buy_filled', 'is_sell_filled',
                 'buy_order_id', 'sell_order_id', 'created_at')
        read_only_fields = ('id', 'created_at')


class StrategyDetailSerializer(serializers.ModelSerializer):
    """策略详情序列化器"""
    strategy_type_display = serializers.CharField(source='get_strategy_type_display', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    stock_name = serializers.SerializerMethodField()
    running_time_display = serializers.SerializerMethodField()
    executions = StrategyExecutionSerializer(many=True, read_only=True, source='strategyexecution_set')
    grid_levels = serializers.SerializerMethodField()
    
    class Meta:
        model = TradingStrategy
        fields = ('id', 'strategy_name', 'strategy_type', 'strategy_type_display',
                 'stock_code', 'stock_name', 'status', 'status_display',
                 'total_investment', 'realized_profit', 'realized_profit_rate',
                 'created_at', 'start_time', 'end_time', 'running_time_display',
                 'executions', 'grid_levels',
                 # 网格参数
                 'grid_min_price', 'grid_max_price', 'grid_count', 'grid_investment',
                 # 马丁格尔参数
                 'martin_trigger_rate', 'martin_profit_target', 'martin_initial_amount',
                 'martin_add_amount', 'martin_max_add_times', 'martin_current_add_times')
        read_only_fields = ('id', 'realized_profit', 'realized_profit_rate', 'created_at',
                           'start_time', 'end_time', 'martin_current_add_times')
    
    def get_stock_name(self, obj):
        try:
            stock = Stock.objects.get(stock_code=obj.stock_code)
            return stock.stock_name
        except Stock.DoesNotExist:
            return None
    
    def get_running_time_display(self, obj):
        running_time = obj.running_time
        if running_time:
            days = running_time.days
            hours, remainder = divmod(running_time.seconds, 3600)
            minutes, _ = divmod(remainder, 60)
            return f"{days}天{hours}小时{minutes}分钟"
        return "0分钟"
    
    def get_grid_levels(self, obj):
        if obj.strategy_type == 1:  # 网格交易
            levels = GridLevel.objects.filter(strategy=obj).order_by('level')
            return GridLevelSerializer(levels, many=True).data
        return []
