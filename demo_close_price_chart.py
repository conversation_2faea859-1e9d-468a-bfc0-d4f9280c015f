#!/usr/bin/env python
"""
演示收盘价走势图功能
"""

import os
import sys
import django
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from matplotlib import rcParams

# 设置中文字体
rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
rcParams['axes.unicode_minus'] = False

# 添加Django项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stock_management.settings')
django.setup()

from stocks.models import Stock, StockDailyData

def create_sample_data():
    """创建示例数据"""
    print("创建示例数据...")
    
    # 创建示例股票
    stock, created = Stock.objects.get_or_create(
        stock_code='000001',
        defaults={
            'stock_name': '平安银行',
            'market': 'SZ',
            'is_active': True
        }
    )
    
    if created:
        print(f"✓ 创建股票: {stock.stock_name} ({stock.stock_code})")
    else:
        print(f"✓ 股票已存在: {stock.stock_name} ({stock.stock_code})")
    
    # 创建示例日线数据（如果不存在）
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=30)
    
    existing_count = StockDailyData.objects.filter(
        stock_code='000001',
        trade_date__gte=start_date
    ).count()
    
    if existing_count == 0:
        print("创建示例日线数据...")
        import random
        from decimal import Decimal
        
        base_price = 10.0
        daily_data = []
        
        for i in range(30):
            trade_date = start_date + timedelta(days=i)
            
            # 跳过周末
            if trade_date.weekday() >= 5:
                continue
            
            # 模拟价格波动
            change = random.uniform(-0.5, 0.5)
            base_price += change
            base_price = max(base_price, 8.0)  # 最低价格
            
            open_price = base_price + random.uniform(-0.2, 0.2)
            close_price = base_price + random.uniform(-0.2, 0.2)
            high_price = max(open_price, close_price) + random.uniform(0, 0.3)
            low_price = min(open_price, close_price) - random.uniform(0, 0.3)
            
            daily_data.append(StockDailyData(
                stock_code='000001',
                trade_date=trade_date,
                open_price=Decimal(f'{open_price:.2f}'),
                close_price=Decimal(f'{close_price:.2f}'),
                high_price=Decimal(f'{high_price:.2f}'),
                low_price=Decimal(f'{low_price:.2f}'),
                volume=random.randint(1000000, 10000000),
                amount=Decimal(f'{random.randint(10000000, 100000000):.2f}'),
                change_rate=Decimal(f'{change:.2f}'),
                change_amount=Decimal(f'{change:.2f}'),
                turnover_rate=Decimal(f'{random.uniform(0.5, 5.0):.2f}')
            ))
        
        StockDailyData.objects.bulk_create(daily_data)
        print(f"✓ 创建了 {len(daily_data)} 条示例数据")
    else:
        print(f"✓ 已有 {existing_count} 条数据")
    
    return stock

def plot_close_price_chart(stock_code='000001', days=30):
    """绘制收盘价走势图"""
    print(f"\n绘制 {stock_code} 的收盘价走势图...")
    
    try:
        # 获取股票信息
        stock = Stock.objects.get(stock_code=stock_code)
        
        # 获取最近N天的日线数据
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        daily_data = StockDailyData.objects.filter(
            stock_code=stock_code,
            trade_date__gte=start_date,
            trade_date__lte=end_date
        ).order_by('trade_date')
        
        if not daily_data.exists():
            print(f"❌ 股票 {stock_code} 没有数据")
            return False
        
        # 准备数据
        dates = [data.trade_date for data in daily_data]
        close_prices = [float(data.close_price) for data in daily_data]
        
        # 创建图表
        plt.figure(figsize=(12, 8))
        
        # 绘制收盘价线图
        plt.plot(dates, close_prices, linewidth=2, color='#E74C3C', marker='o', markersize=4, label='收盘价')
        
        # 添加填充区域
        plt.fill_between(dates, close_prices, alpha=0.3, color='#E74C3C')
        
        # 设置标题和标签
        plt.title(f'{stock.stock_name} ({stock_code}) 收盘价走势图', fontsize=16, fontweight='bold', pad=20)
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('收盘价 (元)', fontsize=12)
        
        # 格式化x轴日期
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=max(1, len(dates)//10)))
        plt.xticks(rotation=45)
        
        # 添加网格
        plt.grid(True, alpha=0.3, linestyle='--')
        
        # 添加图例
        plt.legend(loc='upper left')
        
        # 添加统计信息
        min_price = min(close_prices)
        max_price = max(close_prices)
        avg_price = sum(close_prices) / len(close_prices)
        
        info_text = f'最高: ¥{max_price:.2f}\n最低: ¥{min_price:.2f}\n平均: ¥{avg_price:.2f}'
        plt.text(0.02, 0.98, info_text, transform=plt.gca().transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图片
        filename = f'{stock_code}_{stock.stock_name}_收盘价走势图_演示.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"✓ 收盘价走势图已保存: {filename}")
        
        # 显示图表
        plt.show()
        
        return True
        
    except Stock.DoesNotExist:
        print(f"❌ 股票 {stock_code} 不存在")
        return False
    except Exception as e:
        print(f"❌ 绘制走势图失败: {e}")
        return False

def compare_chart_types():
    """对比新旧图表类型"""
    print("\n对比图表类型...")
    
    # 获取数据
    stock_code = '000001'
    daily_data = StockDailyData.objects.filter(stock_code=stock_code).order_by('trade_date')[:20]
    
    if not daily_data.exists():
        print("❌ 没有数据进行对比")
        return
    
    dates = [data.trade_date for data in daily_data]
    close_prices = [float(data.close_price) for data in daily_data]
    
    # 计算归一化数据（旧版本方式）
    base_value = close_prices[0]
    normalized_data = [((price - base_value) / base_value * 100) for price in close_prices]
    
    # 创建对比图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 新版本：实际收盘价
    ax1.plot(dates, close_prices, linewidth=2, color='#E74C3C', marker='o', markersize=3)
    ax1.set_title('新版本：实际收盘价走势图', fontsize=14, fontweight='bold')
    ax1.set_ylabel('收盘价 (元)')
    ax1.grid(True, alpha=0.3)
    ax1.tick_params(axis='x', rotation=45)
    
    # 旧版本：归一化百分比
    ax2.plot(dates, normalized_data, linewidth=2, color='#3498DB', marker='s', markersize=3)
    ax2.set_title('旧版本：归一化百分比走势图', fontsize=14, fontweight='bold')
    ax2.set_ylabel('涨跌幅 (%)')
    ax2.set_xlabel('日期')
    ax2.grid(True, alpha=0.3)
    ax2.tick_params(axis='x', rotation=45)
    
    plt.tight_layout()
    plt.savefig('图表类型对比_演示.png', dpi=300, bbox_inches='tight')
    print("✓ 图表对比已保存: 图表类型对比_演示.png")
    plt.show()

def main():
    """主函数"""
    print("=" * 60)
    print("收盘价走势图功能演示")
    print("=" * 60)
    print(f"演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 1. 创建示例数据
        stock = create_sample_data()
        
        # 2. 绘制收盘价走势图
        success = plot_close_price_chart(stock.stock_code)
        
        if success:
            # 3. 对比图表类型
            compare_chart_types()
            
            print("\n" + "=" * 60)
            print("🎉 演示完成！")
            print("\n生成的文件:")
            print("1. 000001_平安银行_收盘价走势图_演示.png")
            print("2. 图表类型对比_演示.png")
            print("\n新功能特点:")
            print("✓ 显示实际收盘价格，而非归一化百分比")
            print("✓ 更直观的价格走势展示")
            print("✓ 支持价格统计信息显示")
            print("✓ 高质量PNG图片输出")
        else:
            print("❌ 演示失败")
    
    except Exception as e:
        print(f"❌ 演示过程中出错: {e}")
        import traceback
        traceback.print_exc()
    
    print("=" * 60)

if __name__ == '__main__':
    main()
