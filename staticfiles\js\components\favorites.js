// 收藏组件
Vue.component('favorites-component', {
    template: `
        <div class="favorites-component">
            <div class="content-card">
                <div class="card-header">
                    <h3>我的收藏</h3>
                    <el-button @click="refreshData" icon="el-icon-refresh">刷新</el-button>
                </div>
                <div class="card-body">
                    <div v-if="loading" class="loading">
                        <i class="el-icon-loading"></i> 加载中...
                    </div>
                    <div v-else-if="favoritesList.length === 0" class="empty-state">
                        <i class="el-icon-star-off"></i>
                        <p>暂无收藏的股票</p>
                        <p>去主界面添加收藏吧</p>
                    </div>
                    <div v-else>
                        <div v-for="favorite in favoritesList" :key="favorite.id" class="stock-item">
                            <div class="stock-info">
                                <div class="stock-code">{{ favorite.stock_code }}</div>
                                <div class="stock-name">{{ favorite.stock_name }}</div>
                                <div class="favorite-info">
                                    <span>收藏日期: {{ favorite.favorite_date | formatDate }}</span>
                                    <span>收藏价格: {{ favorite.favorite_price | formatNumber(3) }}</span>
                                </div>
                            </div>
                            <div class="stock-price">
                                <div class="current-price">{{ favorite.current_price | formatNumber(3) }}</div>
                                <div class="price-change" :class="getPriceColorClass(favorite.profit_since_favorite)">
                                    收藏以来: {{ favorite.profit_since_favorite > 0 ? '+' : '' }}{{ favorite.profit_since_favorite | formatPercent }}
                                </div>
                            </div>
                            <div class="stock-actions">
                                <el-button size="mini" @click="viewStockDetail(favorite.stock_code)">详情</el-button>
                                <el-button size="mini" type="danger" @click="removeFavorite(favorite.stock_code)">删除</el-button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 股票详情对话框 -->
            <el-dialog title="股票详情" :visible.sync="detailDialogVisible" width="80%" :close-on-click-modal="false">
                <stock-detail-component 
                    v-if="selectedStockCode" 
                    :stock-code="selectedStockCode">
                </stock-detail-component>
            </el-dialog>
        </div>
    `,
    
    data() {
        return {
            loading: false,
            favoritesList: [],
            detailDialogVisible: false,
            selectedStockCode: null
        };
    },
    
    mounted() {
        this.loadFavorites();
    },
    
    methods: {
        // 加载收藏列表
        async loadFavorites() {
            this.loading = true;
            const result = await API.favorites.getList();
            this.loading = false;
            
            if (result.success) {
                this.favoritesList = result.data.results || result.data;
            } else {
                this.showError('加载收藏列表失败: ' + result.error);
            }
        },
        
        // 查看股票详情
        viewStockDetail(stockCode) {
            this.selectedStockCode = stockCode;
            this.detailDialogVisible = true;
        },
        
        // 删除收藏
        async removeFavorite(stockCode) {
            try {
                await this.confirmAction('确定要取消收藏这只股票吗？');
                const result = await API.favorites.remove(stockCode);
                if (result.success) {
                    this.showSuccess('取消收藏成功');
                    this.loadFavorites(); // 重新加载列表
                } else {
                    this.showError('取消收藏失败: ' + result.error);
                }
            } catch (error) {
                // 用户取消操作
            }
        },
        
        // 获取价格颜色类
        getPriceColorClass(value) {
            return Utils.getPriceColorClass(value);
        },
        
        // 刷新数据
        refreshData() {
            this.loadFavorites();
        }
    }
});
